'use strict';

const audioUtils = require('../services/roleplay/roleplaysessions/audioUtils');
const {AUDIO_PROCESSING_DEFAULTS} = require('../constants/constant');

module.exports = {
  name: 'roleplaySTTMixin',
  methods: {
    async _handleStudentAudioChunk(state, audioChunk, format, socket) {
      state.chunkQueue.push({audioChunk, format});
      if (state.isHandlingChunk) return;
      state.isHandlingChunk = true;
      try {
        while (state.chunkQueue.length > 0) {
          const {audioChunk: currentAudioChunk, format: currentFormat} = state.chunkQueue.shift();
          if (currentFormat) {
            state.clientAudioFormat = {
              sampleRate: currentFormat.sampleRate || state.clientAudioFormat.sampleRate,
              channels: currentFormat.channels || state.clientAudioFormat.channels,
              bitDepth: currentFormat.bitsPerSample || state.clientAudioFormat.bitDepth,
            };
          }
          state.allAudioChunksForSession.push(currentAudioChunk);
          await this.processVoiceActivityAndStreamSTT(state, currentAudioChunk, socket);
        }
      } catch (err) {
        this.logger.error(`_handleStudentAudioChunk error for session ${state.sessionId}:`, err);
        if (socket) socket.emit('server:error', {message: 'Lỗi xử lý audio chunk phía server.'});
      } finally {
        state.isHandlingChunk = false;
      }
    },

    async processVoiceActivityAndStreamSTT(state, audioChunk, socket) {
      try {
        state.audioBuffer = Buffer.concat([state.audioBuffer, audioChunk]);
        const {sampleRate, channels, bitDepth} = state.clientAudioFormat;
        const windowSamples = state.sherpaVad.config?.sileroVad?.windowSize || audioUtils.VAD_DEFAULTS.windowSize;
        const frameBytes = windowSamples * (bitDepth / 8) * channels;
        while (state.audioBuffer.length >= frameBytes) {
          const frame = state.audioBuffer.slice(0, frameBytes);
          state.audioBuffer = state.audioBuffer.slice(frameBytes);
          const floatFrame = audioUtils.pcm16ToFloat32(frame);
          state.sherpaVad.acceptWaveform(floatFrame);
          if(state.sherpaVad.isDetected()){
            state.lastVoiceActivity = Date.now();
          }
          while (!state.sherpaVad.isEmpty()) {
            const seg = state.sherpaVad.front();
            state.sherpaVad.pop();
            const buf = audioUtils.float32ToPcm16(seg.samples);
            if (buf.length > 0) {
              if (!state.isStudentSpeaking) {
                // Bắt đầu turn
                state.isStudentSpeaking = true;
                state.currentStudentTranscript = '';
                if (state.isAiResponding) {
                  state.isAiInterruptedByStudent = true;
                  if (socket) socket.emit('server:ai_interrupted', {sessionId: state.sessionId});
                }
                const {streamId} = await this.broker.call('roleplay.speechprocessing.initializeSpeechStream', {
                  language: 'vi-VN',
                  sessionId: state.sessionId,
                });
                state.sttStreamId = streamId;
                state.startTurnTime = new Date();
              }
              state.currentStudentAudioChunks.push(buf);
              if (state.sttStreamId) {
                // tiếp tục turn
                await this.broker.call('roleplay.speechprocessing.pushAudioToStream', {
                  streamId: state.sttStreamId,
                  audioChunk: buf,
                });
              }
            }
          }
        }
        if (
          state.isStudentSpeaking &&
          Date.now() - state.lastVoiceActivity > AUDIO_PROCESSING_DEFAULTS.silenceThreshold
        ) {
          console.log("TIME", Date.now() - state.lastVoiceActivity, AUDIO_PROCESSING_DEFAULTS.silenceThreshold)
          state.isStudentSpeaking = false;
          state.isAiInterruptedByStudent = false;
          if (state.sttStreamId) {
            await this.broker.call('roleplay.speechprocessing.closeSpeechStream', {streamId: state.sttStreamId});
            state.sttStreamId = null;
          }
          state.audioBuffer = Buffer.alloc(0);
          // Xử lý turn vừa xong
          if (state.currentStudentTranscript.trim()) {
            let turnAudioId = null;
            if (state.currentStudentAudioChunks && state.currentStudentAudioChunks.length > 0) {
              try {
                const audioInfo = await this.saveStudentTurnAudio(state, [...state.currentStudentAudioChunks]);
                if (audioInfo && audioInfo.fileId) {
                  turnAudioId = audioInfo.fileId;
                }
              } catch (saveError) {
                this.logger.error(`Lỗi khi lưu audio lượt nói của học sinh cho session ${state.sessionId}:`, saveError);
              }
            }
            // Luôn reset currentStudentAudioChunks sau khi đã xử lý (lưu hoặc không)
            state.currentStudentAudioChunks = [];

            const durationInSeconds = (Date.now() - state.startTurnTime) / 1000.0;
            const wordCount = state.currentStudentTranscript.trim().split(/\s+/).filter(Boolean).length;
            const speakSpeed = durationInSeconds > 0 ? wordCount / durationInSeconds : 0;

            state.conversationHistory.push({
              role: 'user',
              content: state.currentStudentTranscript.trim(),
              turnAudioId: turnAudioId, // Thêm ID audio của lượt nói
              duration: durationInSeconds,
              speakSpeed: speakSpeed,
              // timestamp: new Date(state.startTime + offset) // Cân nhắc việc sử dụng offset nếu cần chính xác
            });
            console.log('state.currentStudentTranscript.trim()', state.currentStudentTranscript.trim());
            if (state.socket) {
              state.socket.emit('server:student_text_response', {
                sessionId: state.sessionId,
                text: state.currentStudentTranscript.trim(),
                role: 'user',
                isFinal: true,
                turnAudioId: turnAudioId, // Có thể gửi kèm về client nếu cần
              });
            }
            await this.processLLMResponseStreamAll(state, state.socket);
          }
        }
      } catch (err) {
        this.logger.error(`processVoiceActivityAndStreamSTT error for session ${state.sessionId}:`, err);
        if (state.sttStreamId)
          await this.broker.call('roleplay.speechprocessing.closeSpeechStream', {streamId: state.sttStreamId});
        state.isStudentSpeaking = false;
        state.audioBuffer = Buffer.alloc(0);
        if (socket) socket.emit('server:error', {message: 'Lỗi xử lý giọng nói của bạn.'});
      }
    },
  },
};
