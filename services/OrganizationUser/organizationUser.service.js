const DbMongoose = require("../../mixins/dbMongo.mixin");
const OrganizationModel = require("./organizationUser.model");
const BaseService = require("../../mixins/baseService.mixin");
const { SERVICE_NAME } = require("./index");
const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const AuthRole = require("../../mixins/authRole.mixin");

module.exports = {
  name: SERVICE_NAME,
  mixins: [DbMongoose(OrganizationModel), BaseService, FunctionsCommon, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {
      userId: { type: "string", min: 1 },
      organizationId: { type: "string", min: 1 },
    },
    populates: {
      "organizationId": 'organizations.get',
      "userId": 'users.get',
    },
    populateOptions: ["organizationId", "userId"]
  },

  hooks: {
    after: {
      find(ctx, res) {
        return res
      }
    }
  },

  actions: {},
  methods: {},
  events: {
    async "organizationUser.create"(payload, sender, event) {
      this.logger.info("payload", payload, sender, event);
      await this.adapter.insert(payload)
    }
  }
}
