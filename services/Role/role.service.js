const DbMongoose = require("../../mixins/dbMongo.mixin");
const ROLE = require("./role.model");
const { Errors } = require("moleculer");
const BaseService = require("../../mixins/baseService.mixin");
const { SERVICE_NAME } = require("./index");
const i18next = require("i18next");
const AuthRole = require("../../mixins/authRole.mixin");
const { USER_CODES } = require("../../constants/constant");

module.exports = {
  name: SERVICE_NAME,
  mixins: [DbMongoose(ROLE), BaseService, AuthRole],
  settings: {
    /** Validator schema for entity */
    entityValidator: {
      name: { type: "string", min: 1 },
      code: { type: "string", min: 1 },
    },
    populates: {},
    populateOptions: []
  },

  hooks: {
    after: {
      find(ctx, res) {
        return res
      }
    }
  },

  actions: {
    getAllWithoutPagination: {
      role: USER_CODES.NORMAL,
    },
    remove: {
      rest: "DELETE /:id",
      auth: "required",
      params: {
        id: "string",
      },
      /** @param {Context} ctx */
      async handler(ctx) {
        const { id } = ctx.params
        const dataRes = await this.adapter.updateById(id, { isDeleted: true }, { new: true });
        this.broker.emit('roles.deleted', dataRes);
        return dataRes
      }
    },
    create: {
      rest: "POST /",
      auth: "required",
      async handler(ctx) {
        const data = ctx.params
        const checkExistCode = await this.adapter.findOne({ code: data.code, isDeleted: false })
        if (checkExistCode) {
          throw new Errors.MoleculerClientError(i18next.t("role_code_already_exists"));
        }
        return this.adapter.insert(data)
      }
    },
    update: {
      rest: "PUT /:id",
      auth: "required",
      params: {
        id: { type: "string", min: 1 },
      },
      async handler(ctx) {
        const entity = ctx.params;
        await this.validateEntity(entity);
        const checkExistCode = await this.adapter.findOne(
          { code: entity.code, isDeleted: false, _id: { $ne: entity.id } })

        if (checkExistCode) {
          throw new Errors.MoleculerClientError(i18next.t("role_code_already_exists"));
        }

        return entity
      }
    },
  },
  methods: {}
}
