'use strict';

const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const FileMixin = require('../../../mixins/file.mixin');
const Model = require('./references.model');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const i18next = require('i18next');
const {MoleculerClientError} = require('moleculer').Errors;

module.exports = {
  name: 'references',
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, FileMixin],

  settings: {
    entityValidator: {
      name: {type: 'string', min: 1, max: 255},
      type: {type: 'string', enum: ['video', 'youtube', 'docs', 'image', 'url', 'audio']},
      fileId: {type: 'string', optional: true},
      url: {type: 'string', optional: true},
      content: {type: 'string', optional: true},
      organizationId: {type: 'string'},
    },
    populates: {
      fileId: 'files.get',
      organizationId: 'organizations.get',
      createdBy: 'users.get',
      updatedBy: 'users.get',
    },
    populateOptions: ['fileId', 'organizationId', 'createdBy', 'updatedBy'],
    fields: [
      '_id',
      'name',
      'type',
      'fileId',
      'url',
      'content',
      'isPublic',
      'organizationId',
      'createdBy',
      'updatedBy',
      'createdAt',
      'updatedAt',
      'isDeleted',
    ],
  },

  hooks: {
    after: {
      create: async (ctx, reference) => {
        ctx.emit('references.created', {reference});
        return reference;
      },
      remove: async (ctx, reference) => {
        ctx.emit('references.deleted', {reference});
        return reference;
      },
    },
  },

  dependencies: ['files', 'organizations', 'users'],

  actions: {
    // Tạo tài liệu tham khảo mới
    create: {
      rest: 'POST /',
      params: {
        name: {type: 'string', min: 1, max: 255},
        type: {type: 'string', enum: ['video', 'youtube', 'docs', 'image', 'url', 'audio'], optional: true},
        fileId: {type: 'string', optional: true},
        url: {type: 'string', optional: true},
        content: {type: 'string', optional: true},
        organizationId: {type: 'string', optional: true},
        courseId: {type: 'string'},
      },
      async handler(ctx) {
        const {name, type: providedType, fileId, url, content, organizationId, courseId} = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Kiểm tra quyền tạo tài liệu tham khảo (system admin hoặc org admin)
        const hasPermission =
          user.isSystemAdmin ||
          (user.isOrgAdmin && (!organizationId || user.organizationId.toString() === organizationId.toString()));

        if (!hasPermission) {
          throw new MoleculerClientError(
            i18next.t('error.permission_denied', 'Bạn không có quyền tạo tài liệu tham khảo'),
            403,
          );
        }

        // Kiểm tra file tồn tại nếu được chỉ định
        let file = null;
        if (fileId) {
          file = await ctx.call('files.get', {id: fileId}).catch(() => null);

          if (!file) {
            throw new MoleculerClientError(i18next.t('error.file_not_found', 'File không tồn tại'), 404);
          }
        }

        // Tự động xác định loại tài liệu dựa trên url và fileId
        let type = providedType;
        if (!type) {
          if (url && !fileId) {
            // Kiểm tra nếu là URL YouTube
            if (url.includes('youtube.com') || url.includes('youtu.be')) {
              type = 'youtube';
            } else {
              type = 'url';
            }
          } else if (fileId && !url) {
            // Xác định loại dựa trên file extension
            if (file && file.filename) {
              const extension = file.filename.split('.').pop().toLowerCase();
              if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) {
                type = 'video';
              } else if (['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx', 'ppt', 'pptx'].includes(extension)) {
                type = 'docs';
              } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(extension)) {
                type = 'image';
              } else if (['mp3', 'wav', 'ogg', 'flac'].includes(extension)) {
                type = 'audio';
              } else {
                type = 'docs'; // Mặc định là docs nếu không xác định được
              }
            } else {
              type = 'docs'; // Mặc định là docs nếu không có thông tin file
            }
          } else {
            throw new MoleculerClientError(
              i18next.t('error.type_required', 'Không thể tự động xác định loại tài liệu, vui lòng cung cấp type'),
              400,
            );
          }
        }

        // Kiểm tra logic dựa trên loại tài liệu
        if (type === 'url' && !url) {
          throw new MoleculerClientError(i18next.t('error.url_required', 'URL là bắt buộc cho loại tài liệu URL'), 400);
        }

        if ((type === 'video' || type === 'docs' || type === 'image' || type === 'audio') && !fileId) {
          throw new MoleculerClientError(
            i18next.t('error.file_required', 'File là bắt buộc cho loại tài liệu này'),
            400,
          );
        }

        if (type === 'youtube' && !url) {
          throw new MoleculerClientError(
            i18next.t('error.youtube_url_required', 'URL YouTube là bắt buộc cho loại tài liệu YouTube'),
            400,
          );
        }

        // Kiểm tra xem reference loại url hoặc youtube đã tồn tại trong DB chưa
        let existingReference = null;
        if ((type === 'url' || type === 'youtube') && url) {
          // Tìm kiếm reference có cùng URL và không bị xóa
          existingReference = await this.adapter.findOne({
            url: url,
            type: type,
          });
          console.log('existingReference', existingReference);
          // Nếu tìm thấy reference đã tồn tại và có courseId, thêm reference vào khóa học
          if (existingReference && courseId) {
            try {
              await ctx.call('courses.addReferenceToCourse', {
                id: courseId,
                referenceId: existingReference._id.toString(),
              });
              this.logger.info(`Added existing reference ${existingReference._id} to course ${courseId}`);

              // Trả về reference đã tồn tại
              return this.transformDocuments(ctx, {}, existingReference);
            } catch (error) {
              this.logger.error(`Failed to add existing reference to course: ${error.message}`);
              // Không throw lỗi ở đây, tiếp tục tạo reference mới nếu không thêm được
            }
          }
        }

        const referenceData = {
          name,
          type, // Đã được tự động xác định ở trên
          fileId,
          url,
          content,
          organizationId: organizationId || user.organizationId,
          createdBy: user._id,
          updatedBy: user._id,
        };

        let videoDetail = null;
        if (type === 'youtube') {
          videoDetail = await ctx.call('videos.videoDetail', {url: url});
          referenceData.name = videoDetail.title;
        }

        let reference = await this.adapter.insert(referenceData);

        // Xử lý nếu là URL, chuyển đổi thành PDF và trích xuất nội dung
        if (reference.type === 'url' && reference.url) {
          try {
            // Gọi service để chuyển đổi URL thành PDF và trích xuất nội dung
            const {file, text} = await ctx.call('files.extractTextFromUrl', {
              url: reference.url,
              firstPage: 1,
              lastPage: 25,
            });

            // Cập nhật reference với fileId và content
            reference = await this.adapter.updateById(reference._id, {
              $set: {
                fileId: file._id.toString(),
                content: Array.isArray(text) ? text.map(t => t.value).join('\n') : null,
                updatedAt: new Date(),
              },
            });

            this.logger.info(`Extracted content from URL ${reference.url} for reference ${reference._id}`);
          } catch (error) {
            this.logger.error(`Failed to extract content from URL ${reference.url}: ${error.message}`);
            // Do not throw error, proceed with the reference creation without content
          }
        }

        // If the reference is a YouTube video, fetch video details and transcript
        if (reference.type === 'youtube' && reference.url) {
          try {
            // Lấy thông tin chi tiết video từ YouTube
            // Sử dụng lengthSeconds từ thông tin video để lấy transcript đầy đủ
            const cutStart = 0;
            const cutEnd = videoDetail.lengthSeconds || 0;
            console.log('videoDetail', videoDetail);
            console.log('cutStart', cutStart);
            console.log('cutEnd', cutEnd);
            console.log('reference.url', reference.url);

            // Lấy transcript với thông tin thời lượng
            const transcript = await ctx.call('videos.videoTranscript', {
              url: reference.url,
              cutStart: cutStart,
              cutEnd: cutEnd,
            });

            if (transcript && typeof transcript === 'string' && transcript.trim() !== '') {
              // Xử lý transcript bằng OpenAI để cải thiện chất lượng
              const processedTranscript = await this.processTranscriptWithOpenAI(ctx, transcript, videoDetail.title);

              reference = await this.adapter.updateById(reference._id, {
                $set: {
                  content: processedTranscript || transcript, // Sử dụng transcript gốc nếu xử lý thất bại
                  updatedAt: new Date(),
                },
              });
            }
          } catch (error) {
            this.logger.error(`Failed to fetch transcript for YouTube video ${reference.url}: ${error.message}`);
            // Do not throw error, proceed with the reference creation without transcript
          }
        }

        this.broker.emit('references.created', {reference, user});

        // Nếu có courseId, tự động thêm reference vào khóa học
        if (courseId) {
          try {
            await ctx.call('courses.addReferenceToCourse', {
              id: courseId,
              referenceId: reference._id.toString(),
            });
            this.logger.info(`Added reference ${reference._id} to course ${courseId}`);
          } catch (error) {
            this.logger.error(`Failed to add reference to course: ${error.message}`);
            // Không throw lỗi ở đây, vẫn trả về reference đã tạo
          }
        }

        return this.transformDocuments(ctx, {}, reference);
      },
    },

    // Cập nhật tài liệu tham khảo
    update: {
      rest: 'PUT /:id',
      params: {
        id: {type: 'string'},
        name: {type: 'string', min: 1, max: 255, optional: true},
        type: {type: 'string', enum: ['video', 'youtube', 'docs', 'image', 'url', 'audio'], optional: true},
        fileId: {type: 'string', optional: true},
        url: {type: 'string', optional: true},
        content: {type: 'string', optional: true},
      },
      async handler(ctx) {
        const {id, ...updateData} = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Lấy thông tin tài liệu tham khảo để kiểm tra quyền
        const reference = await this.adapter.findById(id);
        if (!reference || reference.isDeleted) {
          throw new MoleculerClientError(
            i18next.t('error.reference_not_found', 'Không tìm thấy tài liệu tham khảo'),
            404,
          );
        }

        // Kiểm tra quyền cập nhật tài liệu tham khảo
        const hasPermission =
          user.isSystemAdmin ||
          (user.isOrgAdmin &&
            reference.organizationId &&
            user.organizationId.toString() === reference.organizationId.toString());

        if (!hasPermission) {
          throw new MoleculerClientError(
            i18next.t('error.permission_denied', 'Bạn không có quyền cập nhật tài liệu tham khảo này'),
            403,
          );
        }

        // Kiểm tra file tồn tại nếu được chỉ định
        if (updateData.fileId) {
          const file = await ctx.call('files.get', {id: updateData.fileId}).catch(() => null);

          if (!file) {
            throw new MoleculerClientError(i18next.t('error.file_not_found', 'File không tồn tại'), 404);
          }
        }

        // Kiểm tra logic dựa trên loại tài liệu nếu type được cập nhật
        const type = updateData.type || reference.type;

        if (type === 'url' && updateData.hasOwnProperty('url') && !updateData.url) {
          throw new MoleculerClientError(i18next.t('error.url_required', 'URL là bắt buộc cho loại tài liệu URL'), 400);
        }

        if (
          (type === 'video' || type === 'docs' || type === 'image' || type === 'audio') &&
          updateData.hasOwnProperty('fileId') &&
          !updateData.fileId
        ) {
          throw new MoleculerClientError(
            i18next.t('error.file_required', 'File là bắt buộc cho loại tài liệu này'),
            400,
          );
        }

        if (type === 'youtube' && updateData.hasOwnProperty('url') && !updateData.url) {
          throw new MoleculerClientError(
            i18next.t('error.youtube_url_required', 'URL YouTube là bắt buộc cho loại tài liệu YouTube'),
            400,
          );
        }

        // Chỉ cho phép cập nhật các trường cụ thể
        const allowedUpdates = ['name', 'type', 'fileId', 'url', 'content', 'isPublic'];
        const finalUpdateData = {};

        for (const key of allowedUpdates) {
          if (updateData.hasOwnProperty(key)) {
            finalUpdateData[key] = updateData[key];
          }
        }
        console.log('finalUpdateData', finalUpdateData);
        console.log('updateData', updateData);
        finalUpdateData.updatedBy = user._id;
        finalUpdateData.updatedAt = new Date();

        const updated = await this.adapter.updateById(id, {$set: finalUpdateData});
        this.broker.emit('references.updated', {reference: updated, user});
        return this.transformDocuments(ctx, {}, updated);
      },
    },

    // Xóa tài liệu tham khảo (xóa mềm)
    remove: {
      rest: 'DELETE /:id',
      params: {
        id: {type: 'string'},
      },
      async handler(ctx) {
        const {id} = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Lấy thông tin tài liệu tham khảo để kiểm tra quyền
        const reference = await this.adapter.findById(id);
        if (!reference || reference.isDeleted) {
          throw new MoleculerClientError(
            i18next.t('error.reference_not_found', 'Không tìm thấy tài liệu tham khảo'),
            404,
          );
        }

        // Kiểm tra quyền xóa tài liệu tham khảo
        const hasPermission =
          user.isSystemAdmin ||
          (user.isOrgAdmin &&
            reference.organizationId &&
            user.organizationId.toString() === reference.organizationId.toString());

        if (!hasPermission) {
          throw new MoleculerClientError(
            i18next.t('error.permission_denied', 'Bạn không có quyền xóa tài liệu tham khảo này'),
            403,
          );
        }

        const updated = await this.adapter.updateById(id, {
          $set: {
            isDeleted: true,
            deletedAt: new Date(),
            updatedBy: user._id,
          },
        });

        this.broker.emit('references.deleted', {referenceId: id, user});
        return {success: true, id};
      },
    },

    // Upload file và tạo tài liệu tham khảo
    uploadAndCreate: {
      async handler(ctx) {
        const {name, organizationId, courseId, url} = ctx.meta.$multipart;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Kiểm tra quyền tạo tài liệu tham khảo
        const hasPermission =
          user.isSystemAdmin ||
          (user.isOrgAdmin && (!organizationId || user.organizationId.toString() === organizationId.toString()));

        if (!hasPermission) {
          throw new MoleculerClientError(
            i18next.t('error.permission_denied', 'Bạn không có quyền tạo tài liệu tham khảo'),
            403,
          );
        }

        // Xử lý upload file
        ctx.meta.$multipart = ctx.meta.$multipart || {};
        const referenceObject = {
          organizationId: organizationId || user.organizationId,
          createdBy: user._id,
          updatedBy: user._id,
        };

        // Xử lý theo loại tài liệu
        let fileId = null;
        let reference = null;
        let type = null;

        // Tự động xác định loại tài liệu dựa trên file extension nếu không có type
        if (!type && ctx.meta.filename) {
          const fileExtension = ctx.meta.filename.split('.').pop().toLowerCase();
          if (['mp4', 'avi', 'mov', 'wmv'].includes(fileExtension)) {
            type = 'video';
          } else if (['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension)) {
            type = 'docs';
          } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(fileExtension)) {
            type = 'image';
          } else if (['mp3', 'wav', 'ogg', 'flac'].includes(fileExtension)) {
            type = 'audio';
          } else {
            type = 'docs'; // Mặc định là docs nếu không xác định được
          }
        }
        let content = null;
        // Xử lý upload file và lấy fileId
        if (ctx.meta.filename) {
          switch (type) {
            case 'image':
              ctx.meta.$multipart.folder = 'images';
              const imageData = await ctx.call('images.upload', ctx.params, {meta: ctx.meta});
              fileId = imageData?._id.toString();
              break;

            case 'audio':
              ctx.meta.$multipart.folder = 'audio';
              const audioData = await ctx.call('files.upload', ctx.params, {meta: ctx.meta});
              await ctx.call('audios.audioTranscript', {audioId: audioData._id.toString()});
              fileId = audioData._id.toString();
              break;

            case 'video':
              ctx.meta.$multipart.folder = 'video';
              const videoData = await ctx.call('offlinevideos.upload', ctx.params, {meta: ctx.meta});
              fileId = videoData._id.toString();
              break;

            case 'docs':
            default:
              ctx.meta.$multipart.folder = 'roleplay/references';
              // const file = await ctx.call('files.upload', ctx.params, {meta: ctx.meta});
              ctx.meta.$multipart.firstPage = 1;
              ctx.meta.$multipart.lastPage = 25;
              const {file, text} = await ctx.call('files.extractTextFromFile', ctx.params, {meta: ctx.meta});
              console.log(file, text);
              fileId = file._id.toString();
              content = text?.map(t => t.value).join('\n');
              console.log('CONTENT', content);
              break;
          }
        }

        // Tạo reference data
        const referenceData = {
          name: name || ctx.meta.filename || 'Untitled Reference',
          type,
          fileId,
          url,
          content,
          organizationId: organizationId || user.organizationId,
          createdBy: user._id,
          updatedBy: user._id,
        };

        reference = await this.adapter.insert(referenceData);

        this.broker.emit('references.created', {reference, user});
        // Nếu có courseId, tự động thêm reference vào khóa học
        if (courseId) {
          try {
            await ctx.call('courses.addReferenceToCourse', {
              id: courseId,
              referenceId: reference._id.toString(),
            });
            this.logger.info(`Added reference ${reference._id} to course ${courseId}`);
          } catch (error) {
            this.logger.error(`Failed to add reference to course: ${error.message}`);
            // Không throw lỗi ở đây, vẫn trả về reference đã tạo
          }
        }

        return this.transformDocuments(ctx, {}, reference);
      },
    },
  },

  events: {
    'references.created': {
      async handler(payload) {
        this.logger.info(`New reference created: ${payload.reference.name}`);
      },
    },

    'references.updated': {
      async handler(payload) {
        this.logger.info(`Reference updated: ${payload.reference._id}`);
      },
    },

    'references.deleted': {
      async handler(payload) {
        this.logger.info(`Reference deleted: ${payload.referenceId}`);
      },
    },
  },

  methods: {
    /**
     * Kiểm tra quyền của user đối với tài liệu tham khảo
     *
     * @param {Object} user - Thông tin user
     * @param {Object} reference - Thông tin tài liệu tham khảo
     * @param {String} permission - Loại quyền ('read', 'write', 'delete')
     * @returns {Boolean} - Có quyền hay không
     */
    hasPermission(user, reference, permission = 'read') {
      if (!user) return false;

      // System admin có mọi quyền
      if (user.isSystemAdmin) return true;

      // Organization admin có quyền với tài liệu tham khảo trong tổ chức của mình
      if (
        user.isOrgAdmin &&
        reference.organizationId &&
        user.organizationId &&
        user.organizationId.toString() === reference.organizationId.toString()
      ) {
        return true;
      }

      // Người dùng thường chỉ có quyền đọc
      if (permission === 'read') {
        // Tài liệu tham khảo thuộc tổ chức của user
        if (
          reference.organizationId &&
          user.organizationId &&
          user.organizationId.toString() === reference.organizationId.toString()
        ) {
          return true;
        }
      }

      return false;
    },

    /**
     * Xử lý transcript bằng OpenAI để cải thiện chất lượng
     *
     * @param {Object} ctx - Context
     * @param {String} transcript - Transcript gốc từ YouTube
     * @param {String} videoTitle - Tiêu đề video
     * @returns {Promise<String>} - Transcript đã được xử lý
     */
    async processTranscriptWithOpenAI(ctx, transcript, videoTitle) {
      try {
        // Tạo prompt cho OpenAI
        const messages = [
          {
            role: 'system',
            content:
              'Bạn là một trợ lý AI chuyên xử lý và cải thiện chất lượng transcript. Nhiệm vụ của bạn là:' +
              '\n1. Sửa lỗi chính tả, ngữ pháp trong transcript' +
              '\n2. Định dạng lại transcript để dễ đọc hơn' +
              '\n3. Loại bỏ các từ lặp lại, từ đệm không cần thiết' +
              '\n4. Giữ nguyên nội dung và thông tin và ví dụ của transcript' +
              '\n5. Tổ chức thành các đoạn có ý nghĩa' +
              '\nKhông thêm nội dung mới hoặc diễn giải lại nội dung.',
          },
          {
            role: 'user',
            content: `Đây là transcript của video có tiêu đề "${videoTitle}". Hãy xử lý và cải thiện chất lượng transcript này:\n\n${transcript}`,
          },
        ];

        // Gọi OpenAI để xử lý transcript
        const processedTranscript = await ctx.call('roleplay.openai.sendToOpenAI', {
          messages,
          model: 'gpt-4o-mini', // Hoặc model phù hợp khác
          temperature: 0.3, // Nhiệt độ thấp để đảm bảo tính nhất quán
          maxTokens: 6000, // Đủ lớn để xử lý transcript dài
        });

        return processedTranscript;
      } catch (error) {
        this.logger.error(`Failed to process transcript with OpenAI: ${error.message}`);
        return null; // Trả về null để sử dụng transcript gốc
      }
    },

    async processReferenceContent(ctx, file, type) {
      // Xử lý nội dung tùy theo loại tài liệu
      let content = null;

      if (file && file.url) {
        content = file.url;
      }

      if (type === 'docs' && file && file.extension === 'pdf') {
        try {
          // Trích xuất text từ PDF nếu có thể
          const pdfText = await ctx.call('files.extractTextFromFile', ctx.params);
          if (pdfText) {
            content = pdfText;
          }
        } catch (error) {
          this.logger.error('Error extracting PDF text:', error);
        }
      }

      return content;
    },

    // Trích xuất ID video YouTube từ URL
    extractYouTubeVideoId(url) {
      if (!url) return null;

      // Regex để trích xuất ID video từ các định dạng URL YouTube khác nhau
      const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/i;
      const match = url.match(regex);

      return match ? match[1] : null;
    },

    // Phương thức tạo tài liệu tham khảo loại hình ảnh
    async createImageReference(imageId, referenceData) {
      if (!imageId) return null;

      const imageData = await this.broker.call('images.get', {id: imageId});
      if (!imageData) return null;

      const reference = {
        ...referenceData,
        name: imageData.displayName || 'Untitled Image',
        type: 'image',
        imageId,
        content: {
          width: imageData.width,
          height: imageData.height,
          url: imageData.url,
          thumbnail: imageData.thumbnail,
        },
      };

      return await this.adapter.insert(reference);
    },

    // Phương thức tạo tài liệu tham khảo loại âm thanh
    async createAudioReference(audioId, referenceData) {
      if (!audioId) return null;

      const audioData = await this.broker.call('audios.get', {id: audioId});
      if (!audioData) return null;

      const reference = {
        ...referenceData,
        name: audioData.displayName || 'Untitled Audio',
        type: 'audio',
        audioId,
        content: {
          duration: audioData.duration,
          url: audioData.url,
          transcript: audioData.transcript,
        },
      };

      return await this.adapter.insert(reference);
    },

    // Phương thức tạo tài liệu tham khảo loại video
    async createVideoReference(videoId, referenceData) {
      if (!videoId) return null;

      const videoData = await this.broker.call('offlinevideos.get', {id: videoId});
      if (!videoData) return null;

      const reference = {
        ...referenceData,
        name: videoData.displayName || 'Untitled Video',
        type: 'video',
        offlineVideoId: videoId,
        content: {
          duration: videoData.duration,
          url: videoData.url,
          thumbnail: videoData.thumbnail,
          transcript: videoData.transcript,
        },
      };

      return await this.adapter.insert(reference);
    },

    // Phương thức tạo tài liệu tham khảo loại tài liệu
    async createDocumentReference(fileId, referenceData, content = {}) {
      if (!fileId) return null;

      const fileData = await this.broker.call('files.get', {id: fileId});
      if (!fileData) return null;

      const reference = {
        ...referenceData,
        name: fileData.displayName || 'Untitled Document',
        type: 'docs',
        fileId,
        content,
      };

      return await this.adapter.insert(reference);
    },
  },

  async started() {
    this.logger.warn('#####################################################References service started');
  },

  async stopped() {
    this.logger.warn('#####################################################References service stopped');
  },
};
