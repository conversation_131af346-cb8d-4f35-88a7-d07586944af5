'use strict';

const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const { MoleculerClientError } = require('moleculer').Errors;
const mongoose = require('mongoose');

module.exports = {
  name: 'roleplay.statistics',
  mixins: [FunctionsCommon, BaseService],

  settings: {
    // Cache settings for performance
    cacheCleanEventName: 'cache.clean.statistics',
    cacheTTL: 300, // 5 minutes cache
  },

  actions: {
    /**
     * Get comprehensive course statistics
     */
    getCourseStatistics: {
      rest: 'GET /courses/:courseId/statistics',
      params: {
        courseId: 'string',
        startDate: { type: 'string', optional: true },
        endDate: { type: 'string', optional: true },
      },
      cache: {
        keys: ['courseId', 'startDate', 'endDate', 'topStudentsLimit'],
        ttl: 300
      },
      async handler(ctx) {
        const { courseId, startDate, endDate, topStudentsLimit } = ctx.params;

        try {
          // Validate course exists
          const course = await this.broker.call('courses.get', { id: courseId });
          if (!course) {
            throw new MoleculerClientError('Course not found', 404);
          }

          // Build date filter
          const dateFilter = this.buildDateFilter(startDate, endDate);

          // Get all statistics in parallel
          const [
            scenarioCount,
            enrollmentStats,
            completionStats,
            topStudents,
            averageScore,
            completionRate
          ] = await Promise.all([
            this.getScenarioCount(courseId),
            this.getEnrollmentStats(courseId, dateFilter),
            this.getCompletionStats(courseId, dateFilter),
            this.getTopStudents(courseId, topStudentsLimit, dateFilter),
            this.getCourseAverageScore(courseId, dateFilter),
            this.getCourseCompletionRate(courseId, dateFilter)
          ]);
          console.log("completionRate",completionRate);
          console.log("averageScore",averageScore);
          console.log("topStudents",topStudents);
          return {
            courseId,
            courseName: course.name,
            period: { startDate, endDate },
            totalScenarios: scenarioCount,
            enrolledStudents: enrollmentStats.totalEnrolled,
            completedStudents: completionStats.totalCompleted,
            completionRate: completionRate,
            averageScore: averageScore,
            topStudents: topStudents,
            generatedAt: new Date()
          };

        } catch (error) {
          this.logger.error('Error getting course statistics:', error);
          throw error;
        }
      }
    },

    /**
     * Get comprehensive student statistics
     */
    getStudentStatistics: {
      rest: 'GET /students/:studentId/statistics',
      params: {
        studentId: 'string',
        startDate: { type: 'string', optional: true },
        endDate: { type: 'string', optional: true }
      },
      cache: {
        keys: ['studentId', 'startDate', 'endDate'],
        ttl: 300
      },
      async handler(ctx) {
        const { studentId, startDate, endDate } = ctx.params;

        try {
          // Validate student exists
          const student = await this.broker.call('users.get', { id: studentId });
          if (!student) {
            throw new MoleculerClientError('Student not found', 404);
          }

          // Build date filter
          const dateFilter = this.buildDateFilter(startDate, endDate);

          // Get all statistics in parallel
          const [
            completedCourses,
            averageScore,
            detailedScores,
            studentRanking,
            completionRate,
            averageCompletionTime,
            bestWorstCourses
          ] = await Promise.all([
            this.getStudentCompletedCourses(studentId, dateFilter),
            this.getStudentAverageScore(studentId, dateFilter),
            this.getStudentDetailedScores(studentId, dateFilter),
            this.getStudentRanking(studentId, dateFilter),
            this.getStudentCompletionRate(studentId, dateFilter),
            this.getStudentAverageCompletionTime(studentId, dateFilter),
            this.getStudentBestWorstCourses(studentId, dateFilter)
          ]);

          return {
            studentId,
            studentName: student.name || student.email,
            period: { startDate, endDate },
            completedCourses: completedCourses.length,
            averageScore: averageScore,
            completionRate: completionRate,
            averageCompletionTimeMinutes: averageCompletionTime,
            ranking: studentRanking,
            bestPerformingCourse: bestWorstCourses.best,
            worstPerformingCourse: bestWorstCourses.worst,
            detailedScores: detailedScores,
            generatedAt: new Date()
          };

        } catch (error) {
          this.logger.error('Error getting student statistics:', error);
          throw error;
        }
      }
    },

    /**
     * Get overall platform statistics
     */
    getOverallStatistics: {
      rest: 'GET /overall',
      params: {
        startDate: { type: 'string', optional: true },
        endDate: { type: 'string', optional: true }
      },
      cache: {
        keys: ['startDate', 'endDate'],
        ttl: 600 // 10 minutes cache for overall stats
      },
      async handler(ctx) {
        const { startDate, endDate } = ctx.params;

        try {
          const dateFilter = this.buildDateFilter(startDate, endDate);

          const [
            totalCourses,
            totalStudents,
            totalSessions,
            totalCompletedSessions,
            averagePlatformScore,
            mostPopularCourses,
            topPerformingStudents
          ] = await Promise.all([
            this.getTotalCourses(),
            this.getTotalActiveStudents(dateFilter),
            this.getTotalSessions(dateFilter),
            this.getTotalCompletedSessions(dateFilter),
            this.getAveragePlatformScore(dateFilter),
            this.getMostPopularCourses(5, dateFilter),
            this.getTopPerformingStudents(10, dateFilter)
          ]);

          return {
            period: { startDate, endDate },
            totalCourses,
            totalActiveStudents: totalStudents,
            totalSessions,
            totalCompletedSessions,
            overallCompletionRate: totalSessions > 0 ? (totalCompletedSessions / totalSessions * 100).toFixed(2) : 0,
            averagePlatformScore,
            mostPopularCourses,
            topPerformingStudents,
            generatedAt: new Date()
          };

        } catch (error) {
          this.logger.error('Error getting overall statistics:', error);
          throw error;
        }
      }
    }
  },

  methods: {
    /**
     * Build MongoDB date filter based on start and end dates
     */
    buildDateFilter(startDate, endDate) {
      const filter = {};

      if (startDate || endDate) {
        filter.createdAt = {};
        if (startDate) {
          filter.createdAt.$gte = new Date(startDate);
        }
        if (endDate) {
          filter.createdAt.$lte = new Date(endDate);
        }
      }

      return filter;
    },

    /**
     * Get total number of scenarios in a course
     */
    async getScenarioCount(courseId) {
      try {
        const scenarios = await this.broker.call('aiscenarios.find', {
          query: {
            courseId: new mongoose.Types.ObjectId(courseId),
            isDeleted: { $ne: true }
          }
        });
        return scenarios.length;
      } catch (error) {
        this.logger.error('Error getting scenario count:', error);
        return 0;
      }
    },

    /**
     * Get enrollment statistics for a course
     */
    async getEnrollmentStats(courseId, dateFilter) {
      try {
        const query = {
          courseId: new mongoose.Types.ObjectId(courseId),
          isDeleted: { $ne: true },
          ...dateFilter
        };

        const sessions = await this.broker.call('roleplaysessions.find', { query });
        const uniqueStudents = new Set(sessions.map(s => s.studentId._id.toString()));

        return {
          totalEnrolled: uniqueStudents.size,
          totalSessions: sessions.length
        };
      } catch (error) {
        this.logger.error('Error getting enrollment stats:', error);
        return { totalEnrolled: 0, totalSessions: 0 };
      }
    },

    /**
     * Get completion statistics for a course
     */
    async getCompletionStats(courseId, dateFilter) {
      try {
        const query = {
          courseId: new mongoose.Types.ObjectId(courseId),
          status: { $in: ['completed', 'analyzed'] },
          isDeleted: { $ne: true },
          ...dateFilter
        };

        const completedSessions = await this.broker.call('roleplaysessions.find', { query });
        const uniqueCompletedStudents = new Set(completedSessions.map(s => s.studentId.toString()));

        return {
          totalCompleted: uniqueCompletedStudents.size,
          totalCompletedSessions: completedSessions.length
        };
      } catch (error) {
        this.logger.error('Error getting completion stats:', error);
        return { totalCompleted: 0, totalCompletedSessions: 0 };
      }
    },

    /**
     * Get top students for a course based on scores
     */
    async getTopStudents(courseId, limit = 10, dateFilter) {
      try {
        // Get all completed sessions for the course
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            courseId: new mongoose.Types.ObjectId(courseId),
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            analysisId: { $exists: true },
            ...dateFilter
          },
          populate: ['studentId', 'analysisId']
        });

        // Group by student and calculate average scores
        const studentScores = {};

        for (const session of sessions) {
          if (!session.analysisId || !session.studentId) continue;

          const studentId = session.studentId._id.toString();
          const score = session.analysisId.result?.simulationScore || 0;

          if (!studentScores[studentId]) {
            studentScores[studentId] = {
              studentId: studentId,
              studentName: session.studentId.name || session.studentId.email,
              scores: [],
              totalSessions: 0
            };
          }

          studentScores[studentId].scores.push(score);
          studentScores[studentId].totalSessions++;
        }

        // Calculate averages and sort
        const topStudents = Object.values(studentScores)
          .map(student => ({
            ...student,
            averageScore: student.scores.length > 0
              ? (student.scores.reduce((sum, score) => sum + score, 0) / student.scores.length).toFixed(2)
              : 0,
            highestScore: student.scores.length > 0 ? Math.max(...student.scores) : 0
          }))
          .sort((a, b) => parseFloat(b.averageScore) - parseFloat(a.averageScore))
          .slice(0, limit);

        return topStudents;
      } catch (error) {
        this.logger.error('Error getting top students:', error);
        return [];
      }
    },

    /**
     * Get average score for a course
     */
    async getCourseAverageScore(courseId, dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            courseId: new mongoose.Types.ObjectId(courseId),
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            analysisId: { $exists: true },
            ...dateFilter
          },
          populate: ['analysisId']
        });
        console.log("sessions",sessions);

        const scores = sessions
          .filter(session => session.analysisId?.result?.simulationScore)
          .map(session => session.analysisId.result.simulationScore);

        if (scores.length === 0) return 0;
        return (scores.reduce((sum, score) => sum + score, 0) / scores.length).toFixed(2);
      } catch (error) {
        this.logger.error('Error getting course average score:', error);
        return 0;
      }
    },

    /**
     * Get course completion rate
     */
    async getCourseCompletionRate(courseId, dateFilter) {
      try {
        const [enrollmentStats, completionStats] = await Promise.all([
          this.getEnrollmentStats(courseId, dateFilter),
          this.getCompletionStats(courseId, dateFilter)
        ]);

        if (enrollmentStats.totalEnrolled === 0) return 0;

        return ((completionStats.totalCompleted / enrollmentStats.totalEnrolled) * 100).toFixed(2);
      } catch (error) {
        this.logger.error('Error getting course completion rate:', error);
        return 0;
      }
    },

    /**
     * Get student's completed courses
     */
    async getStudentCompletedCourses(studentId, dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            studentId: new mongoose.Types.ObjectId(studentId),
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            ...dateFilter
          },
          populate: ['courseId']
        });

        // Get unique courses
        const uniqueCourses = new Map();
        sessions.forEach(session => {
          if (session.courseId) {
            uniqueCourses.set(session.courseId._id.toString(), {
              courseId: session.courseId._id,
              courseName: session.courseId.name,
              completedAt: session.endTime || session.updatedAt
            });
          }
        });

        return Array.from(uniqueCourses.values());
      } catch (error) {
        this.logger.error('Error getting student completed courses:', error);
        return [];
      }
    },

    /**
     * Get student's average score across all courses
     */
    async getStudentAverageScore(studentId, dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            studentId: new mongoose.Types.ObjectId(studentId),
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            analysisId: { $exists: true },
            ...dateFilter
          },
          populate: ['analysisId']
        });

        const scores = sessions
          .filter(session => session.analysisId?.result?.simulationScore)
          .map(session => session.analysisId.result.simulationScore);

        if (scores.length === 0) return 0;

        return (scores.reduce((sum, score) => sum + score, 0) / scores.length).toFixed(2);
      } catch (error) {
        this.logger.error('Error getting student average score:', error);
        return 0;
      }
    },

    /**
     * Get detailed scores for each scenario the student completed
     */
    async getStudentDetailedScores(studentId, dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            studentId: new mongoose.Types.ObjectId(studentId),
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            analysisId: { $exists: true },
            ...dateFilter
          },
          populate: ['courseId', 'aiScenarioId', 'analysisId']
        });

        return sessions
          .filter(session => session.analysisId?.result?.simulationScore)
          .map(session => ({
            sessionId: session._id,
            courseId: session.courseId?._id,
            courseName: session.courseId?.name,
            scenarioId: session.aiScenarioId?._id,
            scenarioName: session.aiScenarioId?.name,
            score: session.analysisId.result.simulationScore,
            completedAt: session.endTime || session.updatedAt,
            duration: session.duration,
            taskScores: session.analysisId.result.knowledgeAnalysis?.taskAnalyses?.map(task => ({
              taskId: task.taskId,
              taskName: task.taskName,
              score: task.score
            })) || []
          }));
      } catch (error) {
        this.logger.error('Error getting student detailed scores:', error);
        return [];
      }
    },

    /**
     * Get student ranking compared to other students
     */
    async getStudentRanking(studentId, dateFilter) {
      try {
        // Get all students with their average scores
        const allSessions = await this.broker.call('roleplaysessions.find', {
          query: {
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            analysisId: { $exists: true },
            ...dateFilter
          },
          populate: ['studentId', 'analysisId']
        });

        // Group by student and calculate averages
        const studentAverages = {};

        allSessions.forEach(session => {
          if (!session.analysisId?.result?.simulationScore || !session.studentId) return;

          const sId = session.studentId._id.toString();
          const score = session.analysisId.result.simulationScore;

          if (!studentAverages[sId]) {
            studentAverages[sId] = {
              studentId: sId,
              scores: [],
              studentName: session.studentId.name || session.studentId.email
            };
          }

          studentAverages[sId].scores.push(score);
        });

        // Calculate averages and sort
        const rankedStudents = Object.values(studentAverages)
          .map(student => ({
            studentId: student.studentId,
            studentName: student.studentName,
            averageScore: student.scores.reduce((sum, score) => sum + score, 0) / student.scores.length,
            totalSessions: student.scores.length
          }))
          .sort((a, b) => b.averageScore - a.averageScore);

        // Find target student's rank
        const targetStudentIndex = rankedStudents.findIndex(s => s.studentId === studentId);

        if (targetStudentIndex === -1) {
          return { rank: null, totalStudents: rankedStudents.length, percentile: null };
        }

        const rank = targetStudentIndex + 1;
        const percentile = ((rankedStudents.length - rank) / rankedStudents.length * 100).toFixed(2);

        return {
          rank,
          totalStudents: rankedStudents.length,
          percentile,
          averageScore: rankedStudents[targetStudentIndex].averageScore.toFixed(2)
        };
      } catch (error) {
        this.logger.error('Error getting student ranking:', error);
        return { rank: null, totalStudents: 0, percentile: null };
      }
    },

    /**
     * Get student's completion rate
     */
    async getStudentCompletionRate(studentId, dateFilter) {
      try {
        const [totalSessions, completedSessions] = await Promise.all([
          this.broker.call('roleplaysessions.find', {
            query: {
              studentId: new mongoose.Types.ObjectId(studentId),
              isDeleted: { $ne: true },
              ...dateFilter
            }
          }),
          this.broker.call('roleplaysessions.find', {
            query: {
              studentId: new mongoose.Types.ObjectId(studentId),
              status: { $in: ['completed', 'analyzed'] },
              isDeleted: { $ne: true },
              ...dateFilter
            }
          })
        ]);

        if (totalSessions.length === 0) return 0;

        return ((completedSessions.length / totalSessions.length) * 100).toFixed(2);
      } catch (error) {
        this.logger.error('Error getting student completion rate:', error);
        return 0;
      }
    },

    /**
     * Get student's average completion time
     */
    async getStudentAverageCompletionTime(studentId, dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            studentId: new mongoose.Types.ObjectId(studentId),
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            duration: { $exists: true },
            ...dateFilter
          }
        });

        const durations = sessions
          .filter(session => session.duration && session.duration > 0)
          .map(session => session.duration);

        if (durations.length === 0) return 0;

        // Return average in minutes
        return Math.round(durations.reduce((sum, duration) => sum + duration, 0) / durations.length / 60);
      } catch (error) {
        this.logger.error('Error getting student average completion time:', error);
        return 0;
      }
    },

    /**
     * Get student's best and worst performing courses
     */
    async getStudentBestWorstCourses(studentId, dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            studentId: new mongoose.Types.ObjectId(studentId),
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            analysisId: { $exists: true },
            ...dateFilter
          },
          populate: ['courseId', 'analysisId']
        });

        // Group by course and calculate averages
        const courseScores = {};

        sessions.forEach(session => {
          if (!session.analysisId?.result?.simulationScore || !session.courseId) return;

          const courseId = session.courseId._id.toString();
          const score = session.analysisId.result.simulationScore;

          if (!courseScores[courseId]) {
            courseScores[courseId] = {
              courseId: courseId,
              courseName: session.courseId.name,
              scores: []
            };
          }

          courseScores[courseId].scores.push(score);
        });

        // Calculate averages
        const courseAverages = Object.values(courseScores)
          .map(course => ({
            courseId: course.courseId,
            courseName: course.courseName,
            averageScore: course.scores.reduce((sum, score) => sum + score, 0) / course.scores.length,
            totalSessions: course.scores.length
          }))
          .sort((a, b) => b.averageScore - a.averageScore);

        return {
          best: courseAverages.length > 0 ? courseAverages[0] : null,
          worst: courseAverages.length > 0 ? courseAverages[courseAverages.length - 1] : null
        };
      } catch (error) {
        this.logger.error('Error getting student best/worst courses:', error);
        return { best: null, worst: null };
      }
    },

    /**
     * Get total number of courses
     */
    async getTotalCourses() {
      try {
        const courses = await this.broker.call('courses.find', {
          query: { isDeleted: { $ne: true } }
        });
        return courses.length;
      } catch (error) {
        this.logger.error('Error getting total courses:', error);
        return 0;
      }
    },

    /**
     * Get total number of active students
     */
    async getTotalActiveStudents(dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            isDeleted: { $ne: true },
            ...dateFilter
          }
        });

        const uniqueStudents = new Set(sessions.map(s => s.studentId.toString()));
        return uniqueStudents.size;
      } catch (error) {
        this.logger.error('Error getting total active students:', error);
        return 0;
      }
    },

    /**
     * Get total number of sessions
     */
    async getTotalSessions(dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            isDeleted: { $ne: true },
            ...dateFilter
          }
        });
        return sessions.length;
      } catch (error) {
        this.logger.error('Error getting total sessions:', error);
        return 0;
      }
    },

    /**
     * Get total number of completed sessions
     */
    async getTotalCompletedSessions(dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            ...dateFilter
          }
        });
        return sessions.length;
      } catch (error) {
        this.logger.error('Error getting total completed sessions:', error);
        return 0;
      }
    },

    /**
     * Get average platform score
     */
    async getAveragePlatformScore(dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            analysisId: { $exists: true },
            ...dateFilter
          },
          populate: ['analysisId']
        });

        const scores = sessions
          .filter(session => session.analysisId?.result?.simulationScore)
          .map(session => session.analysisId.result.simulationScore);

        if (scores.length === 0) return 0;

        return (scores.reduce((sum, score) => sum + score, 0) / scores.length).toFixed(2);
      } catch (error) {
        this.logger.error('Error getting average platform score:', error);
        return 0;
      }
    },

    /**
     * Get most popular courses by enrollment
     */
    async getMostPopularCourses(limit = 5, dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            isDeleted: { $ne: true },
            ...dateFilter
          },
          populate: ['courseId']
        });

        // Count enrollments per course
        const courseEnrollments = {};

        sessions.forEach(session => {
          if (!session.courseId) return;

          const courseId = session.courseId._id.toString();

          if (!courseEnrollments[courseId]) {
            courseEnrollments[courseId] = {
              courseId: courseId,
              courseName: session.courseId.name,
              enrollments: new Set(),
              totalSessions: 0
            };
          }

          courseEnrollments[courseId].enrollments.add(session.studentId.toString());
          courseEnrollments[courseId].totalSessions++;
        });

        // Sort by unique enrollments
        return Object.values(courseEnrollments)
          .map(course => ({
            courseId: course.courseId,
            courseName: course.courseName,
            uniqueEnrollments: course.enrollments.size,
            totalSessions: course.totalSessions
          }))
          .sort((a, b) => b.uniqueEnrollments - a.uniqueEnrollments)
          .slice(0, limit);
      } catch (error) {
        this.logger.error('Error getting most popular courses:', error);
        return [];
      }
    },

    /**
     * Get top performing students across platform
     */
    async getTopPerformingStudents(limit = 10, dateFilter) {
      try {
        const sessions = await this.broker.call('roleplaysessions.find', {
          query: {
            status: { $in: ['completed', 'analyzed'] },
            isDeleted: { $ne: true },
            analysisId: { $exists: true },
            ...dateFilter
          },
          populate: ['studentId', 'analysisId']
        });

        // Group by student and calculate averages
        const studentPerformance = {};

        sessions.forEach(session => {
          if (!session.analysisId?.result?.simulationScore || !session.studentId) return;

          const studentId = session.studentId._id.toString();
          const score = session.analysisId.result.simulationScore;

          if (!studentPerformance[studentId]) {
            studentPerformance[studentId] = {
              studentId: studentId,
              studentName: session.studentId.name || session.studentId.email,
              scores: [],
              totalSessions: 0
            };
          }

          studentPerformance[studentId].scores.push(score);
          studentPerformance[studentId].totalSessions++;
        });

        // Calculate averages and sort
        return Object.values(studentPerformance)
          .filter(student => student.scores.length >= 3) // Minimum 3 sessions for ranking
          .map(student => ({
            studentId: student.studentId,
            studentName: student.studentName,
            averageScore: (student.scores.reduce((sum, score) => sum + score, 0) / student.scores.length).toFixed(2),
            totalSessions: student.totalSessions,
            highestScore: Math.max(...student.scores)
          }))
          .sort((a, b) => parseFloat(b.averageScore) - parseFloat(a.averageScore))
          .slice(0, limit);
      } catch (error) {
        this.logger.error('Error getting top performing students:', error);
        return [];
      }
    }
  }
};
