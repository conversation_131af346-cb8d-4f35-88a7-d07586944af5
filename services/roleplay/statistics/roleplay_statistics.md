# Roleplay Statistics Service

Dịch vụ thống kê và báo cáo chi tiết cho hệ thống roleplay, cung cấp các phân tích toàn diện về khóa học, học viên và hiệu suất tổng thể của nền tảng.

## Tính năng chính

### 1. Th<PERSON><PERSON> kê theo kh<PERSON> học (Course Statistics)
- Đếm tổng số kịch bản (scenarios) trong mỗi khóa học
- Đếm số lượng học viên đã đăng ký/tham gia khóa học
- <PERSON>ế<PERSON> số học viên đã hoàn thành khóa học (điểm >= 70)
- Xế<PERSON> hạng top học viên có điểm số cao nhất
- Tính điểm trung bình của khóa học
- Tỷ lệ hoàn thành khóa học (% sessions đạt điểm >= 70)

### 2. Th<PERSON><PERSON> kê theo <PERSON> (Student Statistics)
- <PERSON><PERSON><PERSON> tổng số khóa học mà học viên đã hoàn thành (có ít nhất 1 session điểm >= 70)
- Tính điểm trung bình của học viên across all courses
- Thống kê điểm số chi tiết cho từng kịch bản
- Xếp hạng học viên so với các học viên khác
- Tỷ lệ hoàn thành các sessions của học viên (% sessions đạt điểm >= 70)
- Thời gian trung bình để hoàn thành một khóa học
- Khóa học có điểm cao nhất/thấp nhất của học viên

### 3. Thống kê tổng thể nền tảng (Overall Platform Statistics)
- Tổng số khóa học, học viên, phiên học
- Tỷ lệ hoàn thành tổng thể
- Điểm trung bình toàn nền tảng
- Khóa học phổ biến nhất
- Top học viên xuất sắc nhất

## API Endpoints

### GET /api/roleplay/statistics/courses/:courseId/statistics
Lấy thống kê chi tiết cho một khóa học cụ thể.

**Parameters:**
- `courseId` (string, required): ID của khóa học
- `startDate` (string, optional): Ngày bắt đầu filter (ISO format)
- `endDate` (string, optional): Ngày kết thúc filter (ISO format)
- `topStudentsLimit` (number, optional, default: 10): Số lượng top học viên

**Response:**
```json
{
  "courseId": "course_id",
  "courseName": "Tên khóa học",
  "period": {
    "startDate": "2024-01-01",
    "endDate": "2024-12-31"
  },
  "totalScenarios": 5,
  "enrolledStudents": 100,
  "completedStudents": 85,
  "completionRate": "85.00",
  "averageScore": "78.50",
  "topStudents": [
    {
      "studentId": "student_id",
      "studentName": "Tên học viên",
      "averageScore": "95.00",
      "totalSessions": 5,
      "highestScore": 98
    }
  ],
  "generatedAt": "2024-01-15T10:30:00.000Z"
}
```

### GET /api/roleplay/statistics/students/:studentId/statistics
Lấy thống kê chi tiết cho một học viên cụ thể.

**Parameters:**
- `studentId` (string, required): ID của học viên
- `startDate` (string, optional): Ngày bắt đầu filter
- `endDate` (string, optional): Ngày kết thúc filter

**Response:**
```json
{
  "studentId": "student_id",
  "studentName": "Tên học viên",
  "period": {
    "startDate": "2024-01-01",
    "endDate": "2024-12-31"
  },
  "completedCourses": 3,
  "averageScore": "82.50",
  "completionRate": "90.00",
  "averageCompletionTimeMinutes": 25,
  "ranking": {
    "rank": 5,
    "totalStudents": 100,
    "percentile": "95.00",
    "averageScore": "82.50"
  },
  "bestPerformingCourse": {
    "courseId": "course_id",
    "courseName": "Khóa học tốt nhất",
    "averageScore": 95.5,
    "totalSessions": 3
  },
  "worstPerformingCourse": {
    "courseId": "course_id",
    "courseName": "Khóa học cần cải thiện",
    "averageScore": 70.0,
    "totalSessions": 2
  },
  "detailedScores": [
    {
      "sessionId": "session_id",
      "courseId": "course_id",
      "courseName": "Tên khóa học",
      "scenarioId": "scenario_id",
      "scenarioName": "Tên kịch bản",
      "score": 85,
      "completedAt": "2024-01-10T15:30:00.000Z",
      "duration": 1800,
      "taskScores": [
        {
          "taskId": "task_id",
          "taskName": "Tên nhiệm vụ",
          "score": 90
        }
      ]
    }
  ],
  "generatedAt": "2024-01-15T10:30:00.000Z"
}
```

### GET /api/roleplay/statistics/overall
Lấy thống kê tổng thể của nền tảng.

**Parameters:**
- `startDate` (string, optional): Ngày bắt đầu filter
- `endDate` (string, optional): Ngày kết thúc filter

**Response:**
```json
{
  "period": {
    "startDate": "2024-01-01",
    "endDate": "2024-12-31"
  },
  "totalCourses": 25,
  "totalActiveStudents": 500,
  "totalSessions": 2500,
  "totalCompletedSessions": 2100,
  "overallCompletionRate": "84.00",
  "averagePlatformScore": "79.50",
  "mostPopularCourses": [
    {
      "courseId": "course_id",
      "courseName": "Khóa học phổ biến",
      "uniqueEnrollments": 150,
      "totalSessions": 300
    }
  ],
  "topPerformingStudents": [
    {
      "studentId": "student_id",
      "studentName": "Học viên xuất sắc",
      "averageScore": "95.50",
      "totalSessions": 10,
      "highestScore": 98
    }
  ],
  "generatedAt": "2024-01-15T10:30:00.000Z"
}
```

## Tính năng kỹ thuật

### Caching
- Sử dụng cache với TTL 5 phút cho course và student statistics
- Cache 10 phút cho overall statistics
- Cache keys dựa trên parameters để đảm bảo tính chính xác

### Performance Optimization
- Sử dụng MongoDB aggregation pipelines cho queries phức tạp
- Parallel execution cho multiple statistics calls
- Proper indexing trên các fields thường query

### Error Handling
- Comprehensive error handling cho tất cả methods
- Graceful degradation khi một phần data không available
- Detailed logging cho debugging

### Date Filtering
- Hỗ trợ filter theo khoảng thời gian tùy chỉnh
- ISO date format support
- Flexible date range queries

## Cách sử dụng

```javascript
// Lấy thống kê khóa học
const courseStats = await broker.call('roleplay.statistics.getCourseStatistics', {
  courseId: 'course_id',
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  topStudentsLimit: 20
});

// Lấy thống kê học viên
const studentStats = await broker.call('roleplay.statistics.getStudentStatistics', {
  studentId: 'student_id',
  startDate: '2024-01-01',
  endDate: '2024-12-31'
});

// Lấy thống kê tổng thể
const overallStats = await broker.call('roleplay.statistics.getOverallStatistics', {
  startDate: '2024-01-01',
  endDate: '2024-12-31'
});
```

## Dependencies

Service này phụ thuộc vào các services sau:
- `courses` - Quản lý khóa học
- `users` - Quản lý người dùng
- `roleplaysessions` - Quản lý phiên roleplay
- `aiscenarios` - Quản lý kịch bản AI
- `roleplay.analysises` - Phân tích kết quả

## Testing

Chạy tests:
```bash
npm test services/roleplay/statistics/statistics.test.js
```

## Notes

- Tất cả scores được làm tròn đến 2 chữ số thập phân
- **Completion Logic**: Một session được xem là "hoàn thành" khi có điểm số >= 70 (thay vì chỉ dựa vào status)
- Completion rate được tính dựa trên sessions có điểm >= 70 so với tổng sessions đã thực hiện
- Student ranking yêu cầu tối thiểu 3 sessions để được xếp hạng
- Time calculations được convert từ seconds sang minutes
- **Score calculation**: Tất cả methods tính điểm trung bình đều bao gồm cả sessions có điểm = 0 để đảm bảo tính nhất quán
- Sessions không có analysisId hoặc không có simulationScore sẽ không được tính vào thống kê
- **Completed courses**: Học viên chỉ được tính là hoàn thành khóa học khi có ít nhất 1 session đạt điểm >= 70
