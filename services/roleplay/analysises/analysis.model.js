"use strict";

const mongoose = require("mongoose");
const Schema = mongoose.Schema;
const { ROLEPLAY_TASKS } = require("../../../constants/dbCollections"); // Added for task reference

// Sub-schema for individual task analysis
const TaskAnalysisSchema = new Schema({
  taskId: {
    type: Schema.Types.ObjectId,
    ref: ROLEPLAY_TASKS,
    required: true
  },
  taskName: { type: String }, // For convenience
  score: { type: Number },
  details: { type: String },
  makeOrBreak: [{ type: String }],
  suggestions: [{ type: String }]
}, {_id: false});

// Schema for analysis results
const AnalysisSchema = new Schema(
  {
    sessionId: {
      type: Schema.Types.ObjectId,
      ref: "RolePlaySession",
      required: true,
      index: true
    },
    // Kết quả phân tích chi tiết
    result: {
      summary: { type: String, required: true },
      topInsights: [{ type: String }],
      simulationScore: { type: Number },
      knowledgeAnalysis: {
        proficiencyProcess: { type: String }, // Overall proficiency for the course
        taskAnalyses: [TaskAnalysisSchema] // Array of analyses for each task
      },
      styleAnalysis: {
        score: { type: Number },
        clarity: { type: String, enum: ['low', 'medium', 'high'] },
        pace: {
          wordsPerMinute: { type: Number },
          evaluation: { type: String }
        },
        fillerWords: {
          count: { type: Number },
          evaluation: { type: String }
        },
        sentenceLength: {
          average: { type: Number },
          evaluation: { type: String }
        },
        energy: { type: String, enum: ['low', 'medium', 'high'] }
      },
      trainerFeedback: {
        generalComments: { type: String },
        improvementSuggestions: [{ type: String }]
      },
      // Các phân tích bổ sung
      emotionAnalysis: { type: Schema.Types.Mixed },
      videoAnalysis: { type: Schema.Types.Mixed },
      softSkillsAnalysis: { type: Schema.Types.Mixed },
      managerFeedback: { type: Schema.Types.Mixed }
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User"
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User"
    },
    isDeleted: {
      type: Boolean,
      default: false,
      index: true
    }
  },
  {
    collection: "RolePlayAnalysis"
  }
);

// Ensure indexes for frequent queries
AnalysisSchema.index({ sessionId: 1, isDeleted: 1 });
AnalysisSchema.index({ createdAt: -1 });

module.exports = mongoose.model("RolePlayAnalysis", AnalysisSchema);
