# PRD - Clickee Role Play Suite

## 1. Giới thiệu

Clickee Role Play Suite là một ứng dụng Web cho phép người dùng là học viên có thể vào các khoá học được cấu hình sẵn các AI Persona theo các kịch bản, ví dụ: kịch bản chăm sóc khách hàng, kịch bản telesale... Mỗi khoá học sẽ có các nhiệm vụ mà học sinh phải hoàn thành với Persona, ví dụ: Một khoá học telesale cho một khách hàng khó tính thì sẽ có 1 AI Persona khó tính và nhiệm vụ là bán được hàng hoá cho khách hàng đấy. Học viên sẽ thực hành nhiệm vụ bằng một mô phỏng video call với AI Persona. <PERSON><PERSON> khi thực hiện video call xong thì sẽ có một màn hình phân tích kết quả dựa vào trao đổi của AI Persona và học viên trong quá trình video call để đưa ra kết quả đánh giá.

### 1.1. Vấn đề cần giải quyết (Problem Statement)
Doanh nghiệp và học viên cần đào tạo kỹ năng bán hàng, kỹ năng giới thiệu sản phẩm cho khách hàng qua điện thoại hoặc gặp trực tiếp nhưng thường không có đủ điều kiện thực hành hiệu quả. Phần mềm Clickee Role Play Suite được phát triển để giúp doanh nghiệp có công cụ đào tạo nhanh chóng, chuẩn hóa quy trình cho nhân viên, đồng thời cung cấp cho học viên một môi trường thực hành sát với thực tế nhất.

### 1.2. Mục tiêu kinh doanh (Business Goals)
Đây là dự án B2B, với mục tiêu chính là bán sản phẩm cho các doanh nghiệp có nhu cầu đào tạo và phát triển kỹ năng cho nhân viên.

### 1.3. Đối tượng người dùng mục tiêu (Target Users)
*   **Học viên:** Những cá nhân muốn nâng cao năng lực, kỹ năng (đặc biệt là kỹ năng mềm như bán hàng, giao tiếp), và mong muốn vận dụng các quy trình đã học vào thực tế công việc.
*   **Admin (Doanh nghiệp/Người quản lý đào tạo):**
    *   Những người chịu trách nhiệm quản lý đào tạo trong doanh nghiệp, tạo nội dung khoá học.
    *   Có kiến thức chuyên môn để xây dựng các khoá học phù hợp với yêu cầu và mục tiêu đào tạo của tổ chức.
    *   Mong muốn có công cụ để đào tạo và kiểm tra kỹ năng, năng lực của nhân viên một cách hiệu quả.

## 2. Chức năng chi tiết

### 2.1. Quản lý của Admin

*   **Tạo Course - Khoá học:**
    *   Mỗi khoá học sẽ có tên.
    *   Tài liệu tham khảo dạng URL, PDF, audio.
*   **Tạo AI Persona:**
    *   Mỗi khoá học sẽ có AI Persona riêng.
    *   Sẽ có một màn hình tạo AI Persona riêng, cho phép cấu hình các thuộc tính sau:
        *   Name (Tên)
        *   Avatar (Ảnh đại diện - dạng tĩnh)
        *   Role (Vai trò)
        *   Mood (Tâm trạng)
        *   Organization (Tổ chức/Công ty mà Persona đại diện)
        *   Small Talk Likely (Khả năng nói chuyện phiếm)
        *   Filter Words (Từ ngữ cần lọc/tránh)
        *   Persona Background (Thông tin nền tảng của Persona)
        *   Persona Concern (Mối quan tâm chính của Persona)
    *   Trong phiên bản MVP, AI Persona sẽ không có khả năng học hỏi hay tự cải thiện qua các lần tương tác.
*   **Cấu hình Nhiệm vụ cho Khoá học:**
    *   Mỗi khoá học có các Nhiệm vụ cần thực hiện.
    *   Mỗi nhiệm vụ lại có mô tả riêng.
    *   Các tiêu chí đánh giá cho nhiệm vụ sẽ dựa trên:
        *   Khả năng hoàn thành mục tiêu/task của nhiệm vụ.
        *   Phân tích tốc độ nói của học viên.
    *   Hệ thống sẽ tự động đánh giá dựa trên các tiêu chí đã cấu hình.
*   **Giới thiệu Khoá học và Nhiệm vụ:**
    *   Mỗi khoá học cũng sẽ có phần giới thiệu khoá học và nhiệm vụ cho học viên.

### 2.2. Chức năng của Học viên

*   **Danh sách Khoá học:**
    *   Học viên sẽ truy cập vào một màn hình danh sách khoá học để lựa chọn.
    *   Mỗi khoá học trong danh sách sẽ có:
        *   Tên khoá học.
        *   Mô tả khoá học.
        *   Thời gian cuộc gọi giả lập.
        *   Loại giả lập: Sale, Service, HR, Education.
*   **Thực hành Video Call:**
    *   Khi vào một khoá học sẽ mở ra một màn hình video call. Giao diện video call sẽ được chia đôi: một nửa hiển thị avatar tĩnh của AI Persona, nửa còn lại hiển thị video của học viên.
    *   Trước khi bắt đầu tương tác chính, sẽ có một phần giới thiệu ngắn về nhiệm vụ và những gì học viên cần thực hiện.
    *   **Giới thiệu ban đầu:** Video call với Clickee Persona để nghe về giới thiệu khoá học cũng như AI Persona mà học viên sắp nói chuyện.
    *   **Tương tác với AI Persona:**
        *   Học viên sẽ nói chuyện với AI Persona.
        *   AI Persona dựa vào mô tả của khoá học, nhiệm vụ khoá học và các tài liệu khoá học cũng như tính cách của AI Persona để trả lời, nói chuyện hoặc hỏi lại học viên.
*   **Đánh giá Kết quả:**
    *   Kết thúc cuộc gọi sẽ có một màn hình đánh giá.
    *   Phân tích kết quả sẽ bao gồm các hạng mục sau:
        *   **Conversation Simulation** (Mô phỏng hội thoại):
            *   Thông tin khóa học và người học
            *   Thời gian thực hiện
            *   Top insights (Những điểm đáng chú ý)
            *   Điểm mô phỏng (Simulation score)
            *   Điểm AI (AI score)
            *   Điểm đạt chuẩn (Pass score)
        *   **AI Trainer Feedback** (Phản hồi từ AI huấn luyện viên):
            *   Nhận xét tổng quát
            *   Gợi ý cải thiện
        *   **Knowledge Analysis** (Phân tích kiến thức):
            *   Điểm kiến thức (50% tổng điểm)
            *   Đánh giá chi tiết cho từng chủ đề trong khóa học
            *   Đánh giá quy trình thành thạo (Proficiency process)
            *   Đánh dấu các yếu tố then chốt (Make or break)
            *   Gợi ý chỉnh sửa và ví dụ
        *   **Style Analysis** (Phân tích phong cách):
            *   Điểm phong cách (50% tổng điểm)
            *   Rõ ràng (Clarity): Thang đo low, medium, high
            *   Tốc độ (Pace): 140-180 từ/phút là tốt
            *   Từ đệm (Filler words): Đánh giá số lần sử dụng "um", "uh",...
            *   Độ dài câu (Sentence length): 1-30 từ là phù hợp
            *   Năng lượng (Energy): Đánh giá sự sinh động trong giọng nói
        *   **Video Analysis** (Phân tích video):
            *   Xem lại video (Playback)
            *   Tải video (Download)
            *   Đánh dấu nội dung (Content markup)
        *   **Soft Skills Analysis** (Phân tích kỹ năng mềm):
            *   Ngôn ngữ cơ thể (Body language)
                *   Tích cực: Tư thế thẳng, cử chỉ tay cởi mở, nghiêng về phía trước
                *   Tiêu cực: Khom lưng, khoanh tay, bồn chồn
            *   Giao tiếp bằng mắt (Eye contact)
                *   Tích cực: Duy trì giao tiếp mắt nhất quán, nhấn mạnh ý quan trọng
                *   Tiêu cực: Nhìn đi chỗ khác quá nhiều, nhìn chằm chằm
            *   Biểu cảm khuôn mặt (Facial expressions)
                *   Tích cực: Biểu cảm tích cực, phù hợp với ngữ cảnh
                *   Tiêu cực: Đảo mắt, thường xuyên nhìn đi chỗ khác
            *   Vị trí cơ thể (Body position)
                *   Tích cực: Đứng/ngồi ở giữa, duy trì khả năng hiển thị, đối diện người nghe
                *   Tiêu cực: Lệch khỏi trung tâm, bị che khuất, không hướng về người nghe
        *   **Manager Feedback** (Phản hồi từ quản lý):
            *   Đánh giá và nhận xét từ giám sát viên hoặc quản lý
    *   Dữ liệu phân tích sẽ được lưu trữ và cho phép học viên xem lại tiến trình của mình.

## 3. Mục tiêu MVP (Minimum Viable Product)
*   Đây là phiên bản Sản phẩm khả dụng tối thiểu (MVP).
*   Tất cả các tính năng được liệt kê trong mục "2. Chức năng chi tiết" đều cần thiết cho phiên bản MVP này.
*   Thứ tự thực hiện các tính năng sẽ phụ thuộc vào sự ưu tiên và tính liên kết giữa chúng (tính năng nào cần có trước để thực hiện tính năng sau đó).
*   Chỉ số đo lường thành công chính cho MVP là "Mức độ hoàn thành nhiệm vụ" của học viên.

## 4. Yêu cầu Phi chức năng (Non-Functional Requirements)

### 4.1. Ưu tiên
*   **Tính dễ sử dụng (Usability):** Giao diện người dùng (UI) và trải nghiệm người dùng (UX) cần trực quan, dễ học, dễ sử dụng cho cả Admin và Học viên.

### 4.2. Chưa ưu tiên trong MVP
*   **Hiệu năng (Performance):** Chưa yêu cầu tối ưu hiệu năng ở mức cao nhất.
*   **Bảo mật (Security):** Các yêu cầu bảo mật ở mức cơ bản, sẽ được nâng cao trong các phiên bản sau.
*   **Khả năng tích hợp (Integration):** Chưa yêu cầu tích hợp với các hệ thống bên ngoài.
