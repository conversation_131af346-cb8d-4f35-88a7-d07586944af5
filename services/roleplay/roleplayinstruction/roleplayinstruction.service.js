const Model = require("./roleplayinstruction.model");
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const BaseService = require('../../../mixins/baseService.mixin');

module.exports = {
  name: 'roleplayinstruction',
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService],
  actions: {
  },

  hooks: {

  },


  started() {
    console.log('RoleplayInstructionService started');
  },

  stopped() {
    console.log('RoleplayInstructionService stopped');
  }
}
