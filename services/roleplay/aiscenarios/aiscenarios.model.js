const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {
  ROLEPLAY_AI_SCENARIOS,
  ROLEPLAY_COURSES,
  ROLEPLAY_AIPERSONAS,
  ROLEPLAY_TASKS,
  ROLEPLAY_INSTRUCTION,
  USER,
  ORGANIZATION,
} = require('../../../constants/dbCollections');

const aiScenarioSchema = new Schema(
  {
    courseId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: ROLEPLAY_COURSES,
      required: true,
      index: true,
    },
    aiPersonaId: {
      // AI Persona được sử dụng trong kịch bản này
      type: mongoose.Schema.Types.ObjectId,
      ref: ROLEPLAY_AIPERSONAS,
      required: true,
    },
    roleplayInstructionId: {
      // Hướng dẫn roleplay cho kịch bản này
      type: mongoose.Schema.Types.ObjectId,
      ref: ROLEPLAY_INSTRUCTION,
    },
    taskIds: [
      {
        // <PERSON>h sách các nhiệm vụ trong kịch bản này
        type: mongoose.Schema.Types.ObjectId,
        ref: ROLEPLAY_TASKS,
      },
    ],
    name: {
      type: String,
      required: true,
      maxlength: 255,
      trim: true,
    },
    description: {
      // Mô tả kịch bản AI
      type: String,
      maxlength: 2000,
      trim: true,
    },

    estimatedCallTimeInMinutes: {
      // Thời gian cuộc gọi ước tính cho kịch bản này (tính bằng phút)
      type: Number,
      min: 0,
    },
    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: ORGANIZATION,
      index: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
    },
    isDeleted: {
      type: Boolean,
      default: false,
      index: true,
    },
    status: {
      type: String,
      enum: ['draft', 'published', 'archived'],
      default: 'draft',
      index: true,
    },
  },
  {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
    versionKey: false,
  },
);

// Indexes
aiScenarioSchema.index({courseId: 1, status: 1});
aiScenarioSchema.index({name: 1, organizationId: 1});
aiScenarioSchema.index({aiPersonaId: 1});

module.exports = mongoose.model(ROLEPLAY_AI_SCENARIOS, aiScenarioSchema, ROLEPLAY_AI_SCENARIOS);
