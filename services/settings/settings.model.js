const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {SETTING, TOOL, FILE} = require('../../constants/dbCollections');
const mongoosePaginate = require('mongoose-paginate-v2');

const schema = new Schema(
  {
    apiKeyOpenAI: {type: String},
    speechKey: {type: String},
    speechPay: {type: Number, default: 0},
    serviceRegion: {type: String},
    isDeleted: {type: Boolean, default: false},
    limitedNumberUser: {type: Number, default: 1000},
    confidenceThreshold: {type: Number, default: 0.2},
    studentTools: [{type: Schema.Types.ObjectId, ref: TOOL}],
    showFeedback: {type: Boolean, default: true},
    userFreeSubmit: {type: Number, default: 10},
    userPaidSubmit: {type: Number, default: 20},
    cronTime: {type: String},
    tutorialUrl: {type: String},
    chatbotId: {type: String},
    showChatbot: {type: Boolean, default: true},
    azureEndpoint: {type: String},
    azureRegion: {type: String},
    telegramBotToken: {type: String},
    telegramChatId: {type: String},
    googleApiKey: {type: String},
    speakingStartAudioId: {type: Schema.Types.ObjectId, ref: FILE},
    clickeeAPIKey: {type: String},
  },
  {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
    versionKey: false,
  },
);
schema.plugin(mongoosePaginate);
module.exports = mongoose.model(SETTING, schema, SETTING);
