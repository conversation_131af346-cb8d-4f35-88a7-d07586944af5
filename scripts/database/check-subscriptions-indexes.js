/**
 * <PERSON><PERSON>t để kiểm tra và tạo indexes cần thiết cho tối ưu hóa getUserTrialExpiredNoUpgrade
 */

const mongoose = require('mongoose');

// Kết nối database
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/lingotutorsuite');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

// Kiểm tra indexes hiện tại
async function checkCurrentIndexes() {
  console.log('\n📊 Checking current indexes...\n');
  
  const subscriptionsIndexes = await mongoose.connection.db.collection('subscriptions').indexes();
  const customersIndexes = await mongoose.connection.db.collection('customers').indexes();
  
  console.log('Subscriptions collection indexes:');
  subscriptionsIndexes.forEach(index => {
    console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
  });
  
  console.log('\nCustomers collection indexes:');
  customersIndexes.forEach(index => {
    console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
  });
  
  return { subscriptionsIndexes, customersIndexes };
}

// Đề xuất indexes cần thiết
function getRecommendedIndexes() {
  return {
    subscriptions: [
      {
        name: 'trial_expired_lookup',
        key: { isFree: 1, endDate: 1, isDeleted: 1 },
        background: true
      },
      {
        name: 'customer_subscription_lookup',
        key: { customerId: 1, isFree: 1, isDeleted: 1 },
        background: true
      },
      {
        name: 'customer_id_index',
        key: { customerId: 1 },
        background: true
      }
    ],
    customers: [
      {
        name: 'user_customer_lookup',
        key: { userId: 1, isDeleted: 1 },
        background: true
      },
      {
        name: 'customer_deleted_index',
        key: { isDeleted: 1 },
        background: true
      }
    ]
  };
}

// Kiểm tra xem index có tồn tại không
function indexExists(existingIndexes, recommendedIndex) {
  return existingIndexes.some(existing => {
    const existingKeys = Object.keys(existing.key);
    const recommendedKeys = Object.keys(recommendedIndex.key);
    
    // Kiểm tra xem có index nào cover được recommended index không
    return recommendedKeys.every(key => existingKeys.includes(key));
  });
}

// Tạo indexes thiếu
async function createMissingIndexes() {
  console.log('\n🔧 Creating missing indexes...\n');
  
  const { subscriptionsIndexes, customersIndexes } = await checkCurrentIndexes();
  const recommended = getRecommendedIndexes();
  
  // Tạo indexes cho subscriptions collection
  console.log('Checking subscriptions indexes:');
  for (const index of recommended.subscriptions) {
    if (!indexExists(subscriptionsIndexes, index)) {
      try {
        await mongoose.connection.db.collection('subscriptions').createIndex(index.key, {
          name: index.name,
          background: index.background
        });
        console.log(`  ✅ Created index: ${index.name}`);
      } catch (error) {
        console.log(`  ❌ Failed to create index ${index.name}:`, error.message);
      }
    } else {
      console.log(`  ⏭️  Index ${index.name} already exists or is covered`);
    }
  }
  
  // Tạo indexes cho customers collection
  console.log('\nChecking customers indexes:');
  for (const index of recommended.customers) {
    if (!indexExists(customersIndexes, index)) {
      try {
        await mongoose.connection.db.collection('customers').createIndex(index.key, {
          name: index.name,
          background: index.background
        });
        console.log(`  ✅ Created index: ${index.name}`);
      } catch (error) {
        console.log(`  ❌ Failed to create index ${index.name}:`, error.message);
      }
    } else {
      console.log(`  ⏭️  Index ${index.name} already exists or is covered`);
    }
  }
}

// Phân tích hiệu suất query
async function analyzeQueryPerformance() {
  console.log('\n📈 Analyzing query performance...\n');
  
  const currentDate = new Date();
  
  // Test aggregation pipeline performance
  const pipeline = [
    {
      $match: {
        isFree: true,
        endDate: { $lt: currentDate },
        isDeleted: { $ne: true }
      }
    },
    {
      $group: {
        _id: "$customerId",
        customerId: { $first: "$customerId" }
      }
    },
    {
      $lookup: {
        from: "subscriptions",
        let: { customerId: "$customerId" },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$customerId", "$$customerId"] },
                  { $eq: ["$isFree", false] },
                  { $ne: ["$isDeleted", true] }
                ]
              }
            }
          },
          { $limit: 1 }
        ],
        as: "paidSubscriptions"
      }
    },
    {
      $match: {
        paidSubscriptions: { $size: 0 }
      }
    },
    {
      $lookup: {
        from: "customers",
        let: { customerId: "$customerId" },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$_id", "$$customerId"] },
                  { $eq: ["$isDeleted", false] },
                  { $ne: ["$userId", null] }
                ]
              }
            }
          }
        ],
        as: "customerInfo"
      }
    },
    {
      $match: {
        "customerInfo.0.userId": { $exists: true }
      }
    },
    {
      $project: {
        _id: 0,
        userId: { $arrayElemAt: ["$customerInfo.userId", 0] }
      }
    }
  ];
  
  try {
    const startTime = Date.now();
    
    // Thêm explain để phân tích performance
    const explainResult = await mongoose.connection.db.collection('subscriptions')
      .aggregate([...pipeline, { $limit: 1 }])
      .explain('executionStats');
    
    const endTime = Date.now();
    
    console.log(`Query execution time: ${endTime - startTime}ms`);
    console.log('Execution stats:');
    console.log(`  - Total docs examined: ${explainResult.executionStats?.totalDocsExamined || 'N/A'}`);
    console.log(`  - Total docs returned: ${explainResult.executionStats?.totalDocsReturned || 'N/A'}`);
    console.log(`  - Execution time: ${explainResult.executionStats?.executionTimeMillis || 'N/A'}ms`);
    
    // Kiểm tra xem có sử dụng index không
    const stages = explainResult.stages || [];
    stages.forEach((stage, index) => {
      if (stage.$cursor && stage.$cursor.queryPlanner) {
        const winningPlan = stage.$cursor.queryPlanner.winningPlan;
        console.log(`  - Stage ${index} winning plan: ${winningPlan.stage}`);
        if (winningPlan.indexName) {
          console.log(`    Using index: ${winningPlan.indexName}`);
        }
      }
    });
    
  } catch (error) {
    console.error('Error analyzing query performance:', error);
  }
}

// Đề xuất tối ưu hóa
function printOptimizationRecommendations() {
  console.log('\n💡 Optimization Recommendations:\n');
  
  console.log('1. Database Indexes:');
  console.log('   - Ensure compound indexes are created for optimal query performance');
  console.log('   - Monitor index usage with db.collection.getIndexes()');
  
  console.log('\n2. Query Optimization:');
  console.log('   - Use aggregation pipeline instead of multiple queries');
  console.log('   - Implement early filtering in pipeline stages');
  console.log('   - Use $limit in lookup pipelines when possible');
  
  console.log('\n3. Caching Strategy:');
  console.log('   - Consider caching results for 5-10 minutes');
  console.log('   - Use time-based cache keys for automatic invalidation');
  
  console.log('\n4. Monitoring:');
  console.log('   - Set up alerts for query execution time > 5 seconds');
  console.log('   - Monitor memory usage during aggregation');
  console.log('   - Track result count trends over time');
  
  console.log('\n5. Scaling Considerations:');
  console.log('   - Consider read replicas for heavy read operations');
  console.log('   - Implement pagination for large result sets');
  console.log('   - Use background jobs for non-real-time requirements');
}

// Main function
async function main() {
  console.log('🚀 Subscriptions Optimization Index Checker\n');
  
  await connectDB();
  
  try {
    await checkCurrentIndexes();
    await createMissingIndexes();
    await analyzeQueryPerformance();
    printOptimizationRecommendations();
    
    console.log('\n✅ Index check and optimization complete!');
    
  } catch (error) {
    console.error('❌ Error during optimization:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  connectDB,
  checkCurrentIndexes,
  createMissingIndexes,
  analyzeQueryPerformance,
  getRecommendedIndexes
};
