/**
 * <PERSON><PERSON>t để test function getUserFeedbackUpgradeIntent
 */

const { ServiceBroker } = require("moleculer");

async function testGetUserFeedbackUpgradeIntent() {
  const broker = new ServiceBroker({ 
    logger: true,
    logLevel: "info"
  });

  try {
    // Load services cần thiết
    broker.loadService("./services/EmailMarketing/groups/groups.service.js");
    broker.loadService("./services/feedback/userFeedback/userFeedbacks.service.js");
    broker.loadService("./services/feedback/submitFeedback/submitFeedbacks.service.js");
    broker.loadService("./services/PaymentModule/subscriptions/subscriptions.service.js");
    broker.loadService("./services/PaymentModule/customers/customers.service.js");
    broker.loadService("./services/users/users.service.js");
    
    await broker.start();
    
    console.log("🚀 Testing getUserFeedbackUpgradeIntent...\n");
    
    // Test 1: Normal implementation
    console.log("=== Test 1: Normal Implementation ===");
    try {
      const startTime = Date.now();
      const result = await broker.call("groups.getUserFeedbackUpgradeIntent");
      const executionTime = Date.now() - startTime;
      
      console.log(`✅ Execution time: ${executionTime}ms`);
      console.log(`📊 Found ${result.length} users with feedback upgrade intent and no paid plans`);
      
      if (result.length > 0) {
        console.log("📋 Sample users:");
        result.slice(0, 3).forEach((user, index) => {
          console.log(`  ${index + 1}. ${user.email} (ID: ${user._id}, Type: ${user.type || 'N/A'})`);
        });
      }
      
    } catch (error) {
      console.error("❌ Normal implementation error:", error.message);
    }
    
    // Test 2: Optimized implementation
    console.log("\n=== Test 2: Optimized Implementation ===");
    try {
      const startTime = Date.now();
      const result = await broker.call("groups.getUserFeedbackUpgradeIntentOptimized");
      const executionTime = Date.now() - startTime;
      
      console.log(`✅ Execution time: ${executionTime}ms`);
      console.log(`📊 Found ${result.length} users (optimized)`);
      
    } catch (error) {
      console.error("❌ Optimized implementation error:", error.message);
    }
    
    // Test 3: Via getUsersByAutomaticType
    console.log("\n=== Test 3: Via getUsersByAutomaticType ===");
    try {
      const startTime = Date.now();
      const result = await broker.call("groups.getUsersByAutomaticType", {
        automaticType: "feedback_upgrade_intent"
      });
      const executionTime = Date.now() - startTime;
      
      console.log(`✅ Execution time: ${executionTime}ms`);
      console.log(`📊 Found ${result.length} users via automatic type`);
      
    } catch (error) {
      console.error("❌ Automatic type call error:", error.message);
    }
    
    // Test 4: Feedback analysis
    console.log("\n=== Test 4: Feedback Analysis ===");
    await analyzeFeedbackData(broker);
    
    // Test 5: Performance comparison
    console.log("\n=== Test 5: Performance Comparison ===");
    await performanceComparison(broker);
    
    console.log("\n✅ Testing completed!");
    
  } catch (error) {
    console.error("❌ Test setup error:", error);
  } finally {
    await broker.stop();
    console.log("👋 Disconnected from services");
  }
}

async function analyzeFeedbackData(broker) {
  try {
    // Lấy tất cả feedback có upgrade intent
    const upgradeFeedbacks = await broker.call("userFeedbacks.find", {
      query: {
        bool: true,
        isDeleted: false
      },
      populate: ["submitFeedbackId"],
      limit: 10
    });
    
    console.log(`📊 Found ${upgradeFeedbacks.length} upgrade intent feedbacks`);
    
    if (upgradeFeedbacks.length > 0) {
      console.log("📋 Sample feedback analysis:");
      
      for (let i = 0; i < Math.min(5, upgradeFeedbacks.length); i++) {
        const feedback = upgradeFeedbacks[i];
        const userId = feedback.submitFeedbackId?.userId;
        
        if (!userId) {
          console.log(`  ${i + 1}. Feedback ${feedback._id}: No userId found`);
          continue;
        }
        
        try {
          // Lấy user info
          const user = await broker.call("users.get", { id: userId });
          
          if (!user) {
            console.log(`  ${i + 1}. Feedback ${feedback._id}: User ${userId} not found`);
            continue;
          }
          
          // Lấy customer info
          const customer = await broker.call("customers.getOneByUser", { userId: user._id });
          
          if (!customer) {
            console.log(`  ${i + 1}. ${user.email}: No customer found`);
            continue;
          }
          
          // Kiểm tra paid subscriptions
          const paidSubscriptions = await broker.call("subscriptions.find", {
            query: {
              customerId: customer._id,
              isFree: false
            }
          });
          
          console.log(`  ${i + 1}. ${user.email}:`);
          console.log(`     - User ID: ${user._id}`);
          console.log(`     - Customer ID: ${customer._id}`);
          console.log(`     - Paid subscriptions: ${paidSubscriptions.length}`);
          console.log(`     - Feedback date: ${feedback.createdAt?.toISOString().split('T')[0] || 'N/A'}`);
          console.log(`     - Qualifies: ${paidSubscriptions.length === 0 ? 'YES' : 'NO'}`);
          
        } catch (error) {
          console.log(`  ${i + 1}. Error analyzing feedback ${feedback._id}: ${error.message}`);
        }
      }
    }
    
    // Thống kê tổng quan
    const allFeedbacks = await broker.call("userFeedbacks.find", {
      query: { isDeleted: false },
      limit: 1000
    });
    
    const upgradeIntentCount = allFeedbacks.filter(f => f.bool === true).length;
    const noUpgradeIntentCount = allFeedbacks.filter(f => f.bool === false).length;
    const neutralCount = allFeedbacks.filter(f => f.bool === null || f.bool === undefined).length;
    
    console.log("\n📈 Feedback Statistics:");
    console.log(`  - Total feedbacks: ${allFeedbacks.length}`);
    console.log(`  - Upgrade intent (bool: true): ${upgradeIntentCount}`);
    console.log(`  - No upgrade intent (bool: false): ${noUpgradeIntentCount}`);
    console.log(`  - Neutral/No response: ${neutralCount}`);
    console.log(`  - Upgrade intent rate: ${((upgradeIntentCount / allFeedbacks.length) * 100).toFixed(2)}%`);
    
  } catch (error) {
    console.error("❌ Feedback analysis error:", error.message);
  }
}

async function performanceComparison(broker) {
  const iterations = 3;
  
  console.log(`🔧 Running performance comparison (${iterations} iterations)...`);
  
  const normalTimes = [];
  const optimizedTimes = [];
  
  for (let i = 0; i < iterations; i++) {
    try {
      // Test normal implementation
      const normalStart = Date.now();
      const normalResult = await broker.call("groups.getUserFeedbackUpgradeIntent");
      const normalTime = Date.now() - normalStart;
      normalTimes.push(normalTime);
      
      // Test optimized implementation
      const optimizedStart = Date.now();
      const optimizedResult = await broker.call("groups.getUserFeedbackUpgradeIntentOptimized");
      const optimizedTime = Date.now() - optimizedStart;
      optimizedTimes.push(optimizedTime);
      
      console.log(`  Run ${i + 1}: Normal ${normalTime}ms (${normalResult.length} results) | Optimized ${optimizedTime}ms (${optimizedResult.length} results)`);
      
    } catch (error) {
      console.log(`  Run ${i + 1}: Error - ${error.message}`);
    }
  }
  
  if (normalTimes.length > 0 && optimizedTimes.length > 0) {
    const avgNormal = normalTimes.reduce((a, b) => a + b, 0) / normalTimes.length;
    const avgOptimized = optimizedTimes.reduce((a, b) => a + b, 0) / optimizedTimes.length;
    const improvement = ((avgNormal - avgOptimized) / avgNormal * 100).toFixed(2);
    
    console.log(`📊 Average Normal: ${avgNormal.toFixed(2)}ms`);
    console.log(`📊 Average Optimized: ${avgOptimized.toFixed(2)}ms`);
    console.log(`📊 Performance improvement: ${improvement}%`);
  }
}

// Helper function để format date
function formatDate(date) {
  return date ? date.toISOString().split('T')[0] : 'N/A';
}

// Helper function để tính số ngày từ hiện tại
function daysSince(date) {
  if (!date) return 'N/A';
  const now = new Date();
  const diffTime = now - date;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}

// Chạy test nếu được gọi trực tiếp
if (require.main === module) {
  testGetUserFeedbackUpgradeIntent().catch(console.error);
}

module.exports = {
  testGetUserFeedbackUpgradeIntent,
  analyzeFeedbackData,
  performanceComparison,
  formatDate,
  daysSince
};
