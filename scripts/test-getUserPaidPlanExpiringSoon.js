/**
 * <PERSON>ript để test function getUserPaidPlanExpiringSoon
 */

const { ServiceBroker } = require("moleculer");

async function testGetUserPaidPlanExpiringSoon() {
  const broker = new ServiceBroker({ 
    logger: true,
    logLevel: "info"
  });

  try {
    // Load services cần thiết
    broker.loadService("./services/EmailMarketing/groups/groups.service.js");
    broker.loadService("./services/PaymentModule/subscriptions/subscriptions.service.js");
    broker.loadService("./services/PaymentModule/customers/customers.service.js");
    broker.loadService("./services/users/users.service.js");
    
    await broker.start();
    
    console.log("🚀 Testing getUserPaidPlanExpiringSoon...\n");
    
    // Test 1: Direct method call
    console.log("=== Test 1: Direct Method Call ===");
    try {
      const startTime = Date.now();
      const result = await broker.call("groups.getUserPaidPlanExpiringSoon");
      const executionTime = Date.now() - startTime;
      
      console.log(`✅ Execution time: ${executionTime}ms`);
      console.log(`📊 Found ${result.length} users with paid plan expiring soon`);
      
      if (result.length > 0) {
        console.log("📋 Sample users:");
        result.slice(0, 3).forEach((user, index) => {
          console.log(`  ${index + 1}. ${user.email} (ID: ${user._id})`);
        });
      }
      
    } catch (error) {
      console.error("❌ Direct method call error:", error.message);
    }
    
    // Test 2: Via getUsersByAutomaticType
    console.log("\n=== Test 2: Via getUsersByAutomaticType ===");
    try {
      const startTime = Date.now();
      const result = await broker.call("groups.getUsersByAutomaticType", {
        automaticType: "paid_plan_expiring_soon"
      });
      const executionTime = Date.now() - startTime;
      
      console.log(`✅ Execution time: ${executionTime}ms`);
      console.log(`📊 Found ${result.length} users via automatic type`);
      
    } catch (error) {
      console.error("❌ Automatic type call error:", error.message);
    }
    
    // Test 3: Performance comparison với các automatic types khác
    console.log("\n=== Test 3: Performance Comparison ===");
    const automaticTypes = [
      'trial_expired_no_upgrade',
      'paid_plan_expiring_soon',
      'never_used_after_signup',
      'inactive_over_14_days'
    ];
    
    for (const type of automaticTypes) {
      try {
        const startTime = Date.now();
        const result = await broker.call("groups.getUsersByAutomaticType", {
          automaticType: type
        });
        const executionTime = Date.now() - startTime;
        
        console.log(`📈 ${type}: ${result.length} users (${executionTime}ms)`);
        
      } catch (error) {
        console.log(`❌ ${type}: Error - ${error.message}`);
      }
    }
    
    // Test 4: Detailed analysis
    console.log("\n=== Test 4: Detailed Analysis ===");
    try {
      // Lấy sample students để test logic
      const students = await broker.call('users.find', {
        query: {
          type: 'student',
          isDeleted: false,
          active: true
        },
        limit: 5
      });
      
      console.log(`📊 Testing with ${students.length} sample students:`);
      
      for (const student of students) {
        try {
          const [subscriptions, permission] = await Promise.all([
            broker.call("subscriptions.checkExpiringSubscription", { userId: student._id }),
            broker.call("permissions.checkQuota", { userId: student._id })
          ]);
          
          console.log(`👤 ${student.email}:`);
          console.log(`   - Expiring subscription: ${subscriptions}`);
          console.log(`   - Low quota: ${permission}`);
          console.log(`   - Qualifies: ${subscriptions && permission}`);
          
        } catch (error) {
          console.log(`👤 ${student.email}: Error - ${error.message}`);
        }
      }
      
    } catch (error) {
      console.error("❌ Detailed analysis error:", error.message);
    }
    
    console.log("\n✅ Testing completed!");
    
  } catch (error) {
    console.error("❌ Test setup error:", error);
  } finally {
    await broker.stop();
    console.log("👋 Disconnected from services");
  }
}

// Helper function để benchmark performance
async function benchmarkFunction(broker, functionName, iterations = 3) {
  console.log(`\n🔧 Benchmarking ${functionName}...`);
  
  const times = [];
  
  for (let i = 0; i < iterations; i++) {
    const start = Date.now();
    
    try {
      const result = await broker.call(functionName);
      const end = Date.now();
      const time = end - start;
      times.push(time);
      
      console.log(`  Run ${i + 1}: ${time}ms (${result.length} results)`);
      
    } catch (error) {
      console.log(`  Run ${i + 1}: Error - ${error.message}`);
    }
  }
  
  if (times.length > 0) {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log(`📊 Average: ${avgTime.toFixed(2)}ms`);
    console.log(`📊 Min: ${minTime}ms, Max: ${maxTime}ms`);
  }
}

// Chạy test nếu được gọi trực tiếp
if (require.main === module) {
  testGetUserPaidPlanExpiringSoon().catch(console.error);
}

module.exports = {
  testGetUserPaidPlanExpiringSoon,
  benchmarkFunction
};
