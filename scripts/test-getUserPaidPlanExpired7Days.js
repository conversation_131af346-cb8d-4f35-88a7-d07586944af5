/**
 * <PERSON>ript để test function getUserPaidPlanExpired7Days
 */

const { ServiceBroker } = require("moleculer");

async function testGetUserPaidPlanExpired7Days() {
  const broker = new ServiceBroker({ 
    logger: true,
    logLevel: "info"
  });

  try {
    // Load services cần thiết
    broker.loadService("./services/EmailMarketing/groups/groups.service.js");
    broker.loadService("./services/PaymentModule/subscriptions/subscriptions.service.js");
    broker.loadService("./services/PaymentModule/customers/customers.service.js");
    broker.loadService("./services/users/users.service.js");
    
    await broker.start();
    
    console.log("🚀 Testing getUserPaidPlanExpired7Days...\n");
    
    // Test 1: Normal implementation
    console.log("=== Test 1: Normal Implementation ===");
    try {
      const startTime = Date.now();
      const result = await broker.call("groups.getUserPaidPlanExpired7Days");
      const executionTime = Date.now() - startTime;
      
      console.log(`✅ Execution time: ${executionTime}ms`);
      console.log(`📊 Found ${result.length} users with all paid plans expired over 7 days`);
      
      if (result.length > 0) {
        console.log("📋 Sample users:");
        result.slice(0, 3).forEach((user, index) => {
          console.log(`  ${index + 1}. ${user.email} (ID: ${user._id}, Type: ${user.type})`);
        });
      }
      
    } catch (error) {
      console.error("❌ Normal implementation error:", error.message);
    }
    
    // Test 2: Optimized implementation
    console.log("\n=== Test 2: Optimized Implementation ===");
    try {
      const startTime = Date.now();
      const result = await broker.call("groups.getUserPaidPlanExpired7DaysOptimized");
      const executionTime = Date.now() - startTime;
      
      console.log(`✅ Execution time: ${executionTime}ms`);
      console.log(`📊 Found ${result.length} users (optimized)`);
      
    } catch (error) {
      console.error("❌ Optimized implementation error:", error.message);
    }
    
    // Test 3: Via getUsersByAutomaticType
    console.log("\n=== Test 3: Via getUsersByAutomaticType ===");
    try {
      const startTime = Date.now();
      const result = await broker.call("groups.getUsersByAutomaticType", {
        automaticType: "paid_plan_expired_7_days"
      });
      const executionTime = Date.now() - startTime;
      
      console.log(`✅ Execution time: ${executionTime}ms`);
      console.log(`📊 Found ${result.length} users via automatic type`);
      
    } catch (error) {
      console.error("❌ Automatic type call error:", error.message);
    }
    
    // Test 4: Performance comparison
    console.log("\n=== Test 4: Performance Comparison ===");
    await performanceComparison(broker);
    
    // Test 5: Detailed analysis
    console.log("\n=== Test 5: Detailed Analysis ===");
    await detailedAnalysis(broker);
    
    console.log("\n✅ Testing completed!");
    
  } catch (error) {
    console.error("❌ Test setup error:", error);
  } finally {
    await broker.stop();
    console.log("👋 Disconnected from services");
  }
}

async function performanceComparison(broker) {
  const iterations = 3;
  
  console.log(`🔧 Running performance comparison (${iterations} iterations)...`);
  
  const normalTimes = [];
  const optimizedTimes = [];
  
  for (let i = 0; i < iterations; i++) {
    try {
      // Test normal implementation
      const normalStart = Date.now();
      const normalResult = await broker.call("groups.getUserPaidPlanExpired7Days");
      const normalTime = Date.now() - normalStart;
      normalTimes.push(normalTime);
      
      // Test optimized implementation
      const optimizedStart = Date.now();
      const optimizedResult = await broker.call("groups.getUserPaidPlanExpired7DaysOptimized");
      const optimizedTime = Date.now() - optimizedStart;
      optimizedTimes.push(optimizedTime);
      
      console.log(`  Run ${i + 1}: Normal ${normalTime}ms (${normalResult.length} results) | Optimized ${optimizedTime}ms (${optimizedResult.length} results)`);
      
    } catch (error) {
      console.log(`  Run ${i + 1}: Error - ${error.message}`);
    }
  }
  
  if (normalTimes.length > 0 && optimizedTimes.length > 0) {
    const avgNormal = normalTimes.reduce((a, b) => a + b, 0) / normalTimes.length;
    const avgOptimized = optimizedTimes.reduce((a, b) => a + b, 0) / optimizedTimes.length;
    const improvement = ((avgNormal - avgOptimized) / avgNormal * 100).toFixed(2);
    
    console.log(`📊 Average Normal: ${avgNormal.toFixed(2)}ms`);
    console.log(`📊 Average Optimized: ${avgOptimized.toFixed(2)}ms`);
    console.log(`📊 Performance improvement: ${improvement}%`);
  }
}

async function detailedAnalysis(broker) {
  try {
    // Lấy sample users để analyze
    const users = await broker.call('users.find', {
      query: {
        type: { $in: ['student', 'teacher'] },
        isDeleted: false,
        active: true
      },
      limit: 5
    });
    
    console.log(`📊 Analyzing ${users.length} sample users:`);
    
    const currentDate = new Date();
    const sevenDaysAgo = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    for (const user of users) {
      try {
        // Lấy customer
        const customer = await broker.call("customers.getOneByUser", { userId: user._id });
        
        if (!customer) {
          console.log(`👤 ${user.email}: No customer found`);
          continue;
        }
        
        // Lấy paid subscriptions
        const paidSubscriptions = await broker.call("subscriptions.find", {
          query: {
            customerId: customer._id,
            isFree: false
          }
        });
        
        console.log(`👤 ${user.email} (${user.type}):`);
        console.log(`   - Customer ID: ${customer._id}`);
        console.log(`   - Paid subscriptions: ${paidSubscriptions.length}`);
        
        if (paidSubscriptions.length > 0) {
          let allExpiredOver7Days = true;
          let hasActive = false;
          
          paidSubscriptions.forEach((sub, index) => {
            const isExpiredOver7Days = sub.endDate <= sevenDaysAgo;
            const isActive = sub.status === 'ACTIVE';
            
            if (isActive) hasActive = true;
            if (!isExpiredOver7Days) allExpiredOver7Days = false;
            
            console.log(`     ${index + 1}. Status: ${sub.status}, End: ${sub.endDate.toISOString().split('T')[0]}, Expired>7d: ${isExpiredOver7Days}`);
          });
          
          console.log(`   - Has active: ${hasActive}`);
          console.log(`   - All expired >7 days: ${allExpiredOver7Days}`);
          console.log(`   - Qualifies: ${!hasActive && allExpiredOver7Days}`);
        } else {
          console.log(`   - No paid subscriptions`);
        }
        
      } catch (error) {
        console.log(`👤 ${user.email}: Error - ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error("❌ Detailed analysis error:", error.message);
  }
}

// Helper function để format date
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

// Helper function để tính số ngày từ hiện tại
function daysSince(date) {
  const now = new Date();
  const diffTime = now - date;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}

// Chạy test nếu được gọi trực tiếp
if (require.main === module) {
  testGetUserPaidPlanExpired7Days().catch(console.error);
}

module.exports = {
  testGetUserPaidPlanExpired7Days,
  performanceComparison,
  detailedAnalysis,
  formatDate,
  daysSince
};
