version: "3.2"
services:
  api:
    image: "registry.thinklabs.com.vn:5000/engsuitenewapi:latest"
    deploy:
      replicas: 1
      # placement:
        # constraints: [ node.hostname==thinklabs03 ]
      placement:
        constraints: [node.labels.environment==development]
      restart_policy:
        condition: any
    environment:
      PORT: 3000
      NODE_ENV: "production"
      SERVICE_3000_NAME: "engsuitenewapi"
      SERVICE_NAME: "engsuitenewapi"
      SERVICE_TAGS: "engsuitenewapi"
      DOMAIN: 'https://clickee.ai'
      MONGO_URI: '***********************************************************************************************************'
      OPENAI_API_KEY: '***************************************************'
    ports:
      - target: 3000
        published: 4061
        mode: host
    volumes:
      - uploadsfile:/app/services/File/storage

volumes:
  uploadsfile:
    driver: lizardfs
#volumes:
#  uploadsfile:
#    driver: local
