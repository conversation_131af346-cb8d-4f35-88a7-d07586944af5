version: "3.2"

services:
  api:
    image: "clickeeapi:latest"
    deploy:
      replicas: 1
      restart_policy:
        condition: any
    environment:
      PORT: 3000
      NODE_ENV: "production"
      DOMAIN: 'https://clickee.ai'
      CLIENT_ID: '413043088046-qai6ejq602e2ng5gimdku28s2e19gbch.apps.googleusercontent.com'
      CLIENT_SECRET: '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'
      REDIRECT_URI: 'https://clickee.ai/auth/login-google'
      FORM_CLIENT_ID: '413043088046-n6ikic605fa6ml238c69mi9kaqc88bh6.apps.googleusercontent.com'
      FORM_CLIENT_SECRET: '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'
      FORM_REDIRECT_URI: 'https://clickee.ai/google-form-callback'
      MONGO_URI: '********************************************************************************************************************************'
      OPENAI_API_KEY: '***************************************************'
    ports:
      - target: 3000
        published: 3010
        mode: host
    volumes:
      - uploadsfile:/app/services/File/storage

volumes:
 uploadsfile:
   driver: local
