module.exports = {
  "root": true,
  "env": {
    "node": true,
    "commonjs": true,
    "es6": true,
    "jquery": false,
    "jest": true,
    "jasmine": true
  },
  "extends": "eslint:recommended",
  "parserOptions": {
    "sourceType": "module",
    "ecmaVersion": "2020",
    "parser": "@babel/eslint-parser"
  },
  "rules": {
    "indent": [
      "warn",
      2,
      {"SwitchCase": 1}
    ],
    "no-var": [
      "error"
    ],
    "no-console": [
      "off"
    ],
    "no-unused-vars": [
      "warn"
    ],
    "no-mixed-spaces-and-tabs": [
      "warn"
    ]
  }
};
